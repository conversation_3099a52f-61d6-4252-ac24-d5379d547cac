/* Custom styles */
body {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.footer {
    margin-top: auto;
}

/* Card styles */
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 1.5rem;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0,0,0,.125);
}

/* Table styles */
.table thead th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
}

/* Button styles */
.btn {
    margin-right: 0.5rem;
}

.btn i {
    margin-right: 0.5rem;
}

/* Form styles */
.form-group {
    margin-bottom: 1rem;
}

/* Alert styles */
.alert {
    margin-bottom: 1rem;
}

/* Navigation active state */
.nav-link.active {
    background-color: rgba(255,255,255,0.1);
    border-radius: 0.25rem;
}

/* Berita content styles */
.berita-content {
    line-height: 1.6;
    font-size: 1rem;
    color: #333;
}

.berita-content ul, .berita-content ol {
    margin: 1rem 0;
    padding-left: 2rem;
    list-style-position: outside;
}

.berita-content ul {
    list-style-type: disc;
}

.berita-content ol {
    list-style-type: decimal;
}

.berita-content li {
    margin-bottom: 0.5rem;
    line-height: 1.5;
    display: list-item;
}

.berita-content p {
    margin-bottom: 1rem;
}

.berita-content strong {
    font-weight: bold;
}

.berita-content em {
    font-style: italic;
}

.berita-content h1, .berita-content h2, .berita-content h3,
.berita-content h4, .berita-content h5, .berita-content h6 {
    margin: 1.5rem 0 1rem 0;
    font-weight: bold;
}

.berita-content blockquote {
    margin: 1rem 0;
    padding: 0.5rem 1rem;
    border-left: 4px solid #007bff;
    background-color: #f8f9fa;
}

/* Custom spacing */
.mt-4 {
    margin-top: 2rem !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }
}
