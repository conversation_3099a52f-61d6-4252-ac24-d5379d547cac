<?php
require_once '../config/database.php';

// Get the latest news item to debug
$database = new Database();
$db = $database->getConnection();

$query = "SELECT id, judul, isi FROM berita ORDER BY created_at DESC LIMIT 1";
$stmt = $db->prepare($query);
$stmt->execute();
$row = $stmt->fetch(PDO::FETCH_ASSOC);

if ($row) {
    echo "<h3>Debug Konten Berita</h3>";
    echo "<p><strong>ID:</strong> " . $row['id'] . "</p>";
    echo "<p><strong>Judul:</strong> " . htmlspecialchars($row['judul']) . "</p>";
    echo "<hr>";
    echo "<h4>Raw Content (apa yang tersimpan di database):</h4>";
    echo "<pre>" . htmlspecialchars($row['isi']) . "</pre>";
    echo "<hr>";
    echo "<h4>Rendered Content (bagaimana seharusnya ditampilkan):</h4>";
    echo "<div style='border: 1px solid #ccc; padding: 15px; background: #f9f9f9;'>";
    echo $row['isi'];
    echo "</div>";
} else {
    echo "<p>Tidak ada berita ditemukan.</p>";
}
?>
