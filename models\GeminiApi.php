<?php
require_once __DIR__ . '/../config/database.php';

// Include debug configuration
if (file_exists(__DIR__ . '/../config/debug.php')) {
    require_once __DIR__ . '/../config/debug.php';
}

class GeminiApi {
    private $api_key;
    private $base_url = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent';
    private $debug_enabled = true;

    public function __construct() {
        $database = new Database();
        $this->api_key = $database->getGeminiApiKey();

        // Enable debug logging if debug config exists
        if (defined('DEBUG_ENABLED')) {
            $this->debug_enabled = DEBUG_ENABLED;
        }
    }

    public function generateQuestions($rpp_data, $config) {
        if (empty($this->api_key)) {
            throw new Exception("Gemini API key tidak ditemukan. <PERSON>lakan konfigurasi API key terlebih dahulu.");
        }

        $prompt = $this->buildPrompt($rpp_data, $config);
        
        $data = [
            'contents' => [
                [
                    'parts' => [
                        ['text' => $prompt]
                    ]
                ]
            ],
            'generationConfig' => [
                'temperature' => 0.8,
                'topK' => 40,
                'topP' => 0.95,
                'maxOutputTokens' => 4096,
            ]
        ];

        $response = $this->makeRequest($data);
        
        if (!$response) {
            throw new Exception("Gagal menghubungi API Gemini");
        }

        return $this->parseResponse($response, $config);
    }

    private function buildPrompt($rpp_data, $config) {
        $prompt = "Berdasarkan RPP berikut, buatlah soal-soal sesuai dengan spesifikasi yang diminta:\n\n";
        
        // RPP Information
        $prompt .= "=== INFORMASI RPP ===\n";
        $prompt .= "Mata Pelajaran: " . $rpp_data['nama_mapel'] . "\n";
        $prompt .= "Kelas: " . $rpp_data['nama_kelas'] . "\n";
        $prompt .= "Materi Pokok: " . $rpp_data['materi_pokok'] . "\n";
        $prompt .= "Tujuan Pembelajaran: " . $rpp_data['tujuan_pembelajaran'] . "\n";
        $prompt .= "Kompetensi Dasar: " . $rpp_data['kompetensi_dasar'] . "\n";
        $prompt .= "Indikator Pencapaian: " . $rpp_data['indikator_pencapaian'] . "\n";
        $prompt .= "Materi Pembelajaran: " . $rpp_data['materi_pembelajaran'] . "\n\n";

        // Question Requirements
        $prompt .= "=== SPESIFIKASI SOAL ===\n";
        
        if ($config['multiple_choice_count'] > 0) {
            $prompt .= "Pilihan Ganda: " . $config['multiple_choice_count'] . " soal\n";
            $prompt .= "Jumlah Opsi: " . $config['multiple_choice_options'] . " (A";
            for ($i = 2; $i <= $config['multiple_choice_options']; $i++) {
                $prompt .= "-" . chr(64 + $i);
            }
            $prompt .= ")\n";
        }
        
        if ($config['essay_count'] > 0) {
            $prompt .= "Essay: " . $config['essay_count'] . " soal\n";
        }

        // Difficulty levels
        $prompt .= "\nTingkat Kesulitan:\n";
        if ($config['regular_count'] > 0) {
            $prompt .= "- Regular: " . $config['regular_count'] . " soal\n";
        }
        if ($config['hots_easy_count'] > 0) {
            $prompt .= "- HOTS Mudah: " . $config['hots_easy_count'] . " soal\n";
        }
        if ($config['hots_medium_count'] > 0) {
            $prompt .= "- HOTS Sedang: " . $config['hots_medium_count'] . " soal\n";
        }
        if ($config['hots_hard_count'] > 0) {
            $prompt .= "- HOTS Tinggi: " . $config['hots_hard_count'] . " soal\n";
        }

        $prompt .= "\n=== PANDUAN SOAL HOTS BERKUALITAS ===\n";
        $prompt .= "SOAL HOTS HARUS memenuhi kriteria berikut:\n";
        $prompt .= "1. MENGGUNAKAN SKENARIO/KASUS NYATA yang relevan dengan materi\n";
        $prompt .= "2. MEMERLUKAN ANALISIS MENDALAM, bukan hanya mengingat fakta\n";
        $prompt .= "3. MENGGABUNGKAN MULTIPLE KONSEP dalam satu soal\n";
        $prompt .= "4. MEMINTA EVALUASI, PERBANDINGAN, atau SINTESIS informasi\n";
        $prompt .= "5. MENGGUNAKAN DATA, GRAFIK, atau SITUASI KOMPLEKS\n\n";

        $prompt .= "=== CONTOH SOAL HOTS YANG BENAR ===\n";
        $prompt .= "PILIHAN GANDA HOTS:\n";
        $prompt .= "❌ SALAH: \"Manakah yang termasuk jenis-jenis komputer?\"\n";
        $prompt .= "✅ BENAR: \"Berdasarkan data penggunaan komputer di perusahaan X (desktop 60%, laptop 30%, tablet 10%), strategi manakah yang paling efektif untuk meningkatkan produktivitas karyawan?\"\n\n";

        $prompt .= "ESSAY HOTS:\n";
        $prompt .= "❌ SALAH: \"Jelaskan pengertian algoritma\"\n";
        $prompt .= "✅ BENAR: \"Analisis mengapa algoritma bubble sort tidak efisien untuk dataset besar, kemudian evaluasi alternatif algoritma yang lebih sesuai dengan memberikan justifikasi berdasarkan kompleksitas waktu dan ruang.\"\n\n";

        $prompt .= "=== KATA KERJA OPERASIONAL YANG TEPAT ===\n";
        $prompt .= "PILIHAN GANDA:\n";
        $prompt .= "- Regular: pilih, tentukan, identifikasi, tunjukkan, sebutkan\n";
        $prompt .= "- HOTS: bandingkan, kategorikan, klasifikasikan, bedakan, prediksi, evaluasi mana yang, tentukan strategi terbaik\n\n";
        $prompt .= "ESSAY:\n";
        $prompt .= "- Regular: jelaskan, uraikan, sebutkan dan jelaskan\n";
        $prompt .= "- HOTS: analisis, evaluasi, sintesis, rancang, ciptakan, kritik, bandingkan dan kontraskan, justifikasi\n\n";

        $prompt .= "=== FORMAT OUTPUT ===\n";
        $prompt .= "Berikan output dalam format JSON yang valid dengan struktur berikut:\n\n";
        $prompt .= "```json\n";
        $prompt .= "{\n";
        $prompt .= "  \"questions\": [\n";
        $prompt .= "    {\n";
        $prompt .= "      \"type\": \"multiple_choice\",\n";
        $prompt .= "      \"difficulty\": \"regular\",\n";
        $prompt .= "      \"question\": \"Manakah yang merupakan contoh perangkat input?\",\n";
        $prompt .= "      \"options\": [\"A. Keyboard\", \"B. Monitor\", \"C. Speaker\", \"D. Printer\"],\n";
        $prompt .= "      \"correct_answer\": \"A\"\n";
        $prompt .= "    },\n";
        $prompt .= "    {\n";
        $prompt .= "      \"type\": \"multiple_choice\",\n";
        $prompt .= "      \"difficulty\": \"hots_medium\",\n";
        $prompt .= "      \"question\": \"Sebuah perusahaan startup memiliki budget terbatas untuk membeli komputer. Data kebutuhan: 10 karyawan admin (tugas ringan), 5 programmer (coding), 3 designer (grafis). Strategi pembelian manakah yang paling cost-effective?\",\n";
        $prompt .= "      \"options\": [\"A. Semua menggunakan laptop high-end\", \"B. Admin: desktop basic, Programmer: workstation, Designer: desktop grafis\", \"C. Semua menggunakan desktop mid-range\", \"D. Semua menggunakan tablet dengan keyboard\"],\n";
        $prompt .= "      \"correct_answer\": \"B\"\n";
        $prompt .= "    },\n";
        $prompt .= "    {\n";
        $prompt .= "      \"type\": \"essay\",\n";
        $prompt .= "      \"difficulty\": \"hots_hard\",\n";
        $prompt .= "      \"question\": \"Analisis dampak implementasi sistem cloud computing terhadap efisiensi operasional perusahaan, kemudian evaluasi risiko keamanan yang mungkin timbul dan rancang strategi mitigasi yang komprehensif!\"\n";
        $prompt .= "    }\n";
        $prompt .= "  ]\n";
        $prompt .= "}\n";
        $prompt .= "```\n\n";

        $prompt .= "ATURAN PENTING:\n";
        $prompt .= "1. Pastikan soal sesuai dengan materi dan tujuan pembelajaran RPP\n";
        $prompt .= "2. KHUSUS SOAL HOTS - WAJIB memenuhi kriteria berikut:\n";
        $prompt .= "   - HOTS Easy: Gunakan skenario sederhana + analisis dasar\n";
        $prompt .= "   - HOTS Medium: Gunakan kasus kompleks + evaluasi/perbandingan\n";
        $prompt .= "   - HOTS Hard: Gunakan multiple data + sintesis/kreasi solusi\n";
        $prompt .= "   - SEMUA HOTS: Harus ada konteks nyata, bukan soal hafalan\n";
        $prompt .= "3. KHUSUS PILIHAN GANDA - Gunakan kata kerja yang sesuai untuk format pilihan:\n";
        $prompt .= "   - Regular: pilih, tentukan, sebutkan, identifikasi, tunjukkan\n";
        $prompt .= "   - HOTS: bandingkan, kategorikan, klasifikasikan, bedakan, prediksi, evaluasi strategi\n";
        $prompt .= "   - DILARANG untuk pilihan ganda: jelaskan, uraikan, analisis, evaluasi, deskripsikan, simpulkan\n";
        $prompt .= "4. KHUSUS ESSAY - Gunakan kata kerja yang memerlukan penjelasan tertulis:\n";
        $prompt .= "   - Regular: jelaskan, sebutkan dan jelaskan, uraikan\n";
        $prompt .= "   - HOTS: analisis, evaluasi, sintesis, ciptakan, rancang, kritik, justifikasi\n";
        $prompt .= "5. TEMPLATE SOAL HOTS yang HARUS diikuti:\n";
        $prompt .= "   - Mulai dengan skenario/kasus: \"Sebuah perusahaan/sekolah/situasi...\"\n";
        $prompt .= "   - Berikan data spesifik: angka, persentase, kondisi\n";
        $prompt .= "   - Minta analisis/evaluasi/sintesis berdasarkan data\n";
        $prompt .= "   - Hindari soal yang bisa dijawab dengan hafalan\n";
        $prompt .= "6. Untuk pilihan ganda: satu jawaban benar, distractor masuk akal dan relevan\n";
        $prompt .= "7. Gunakan bahasa Indonesia yang baik dan benar\n";
        $prompt .= "8. Difficulty levels: regular, hots_easy, hots_medium, hots_hard\n";
        $prompt .= "9. Question types: multiple_choice, essay\n";
        $prompt .= "10. VALIDASI WAJIB - Periksa setiap soal sebelum output:\n";
        $prompt .= "    - Pilihan ganda TIDAK BOLEH menggunakan: jelaskan, uraikan, analisis, evaluasi, simpulkan\n";
        $prompt .= "    - Essay HARUS menggunakan kata kerja yang memerlukan penjelasan panjang\n";
        $prompt .= "    - Pilihan ganda harus bisa dijawab dengan memilih satu opsi saja\n";
        $prompt .= "    - HOTS harus memiliki skenario nyata dan memerlukan pemikiran tingkat tinggi\n";
        $prompt .= "11. Berikan HANYA JSON yang valid, tanpa teks tambahan\n\n";

        $prompt .= "PERINGATAN KHUSUS:\n";
        $prompt .= "1. VERB VALIDATION: Jika Anda membuat soal pilihan ganda dengan kata 'jelaskan', 'analisis', atau 'evaluasi',\n";
        $prompt .= "   maka soal tersebut SALAH dan tidak dapat dijawab dengan format pilihan ganda.\n";
        $prompt .= "   Pastikan setiap soal pilihan ganda dapat dijawab hanya dengan memilih A, B, C, atau D.\n\n";
        $prompt .= "2. HOTS QUALITY: Soal HOTS yang hanya mengganti kata kerja tanpa skenario nyata adalah BUKAN HOTS.\n";
        $prompt .= "   Contoh SALAH: 'Analisis jenis-jenis komputer' (ini hanya hafalan dengan kata 'analisis')\n";
        $prompt .= "   Contoh BENAR: 'Berdasarkan kebutuhan kantor dengan 50 karyawan, budget 100 juta, dan ruang terbatas,\n";
        $prompt .= "   evaluasi kombinasi perangkat komputer yang paling optimal untuk meningkatkan produktivitas.'\n\n";
        $prompt .= "3. SKENARIO REQUIREMENT: Setiap soal HOTS HARUS dimulai dengan konteks nyata yang spesifik.\n";

        return $prompt;
    }

    private function makeRequest($data, $retries = 3) {
        $url = $this->base_url . '?key=' . $this->api_key;

        // Log request for debugging
        $this->logDebug("Making API request to: " . $url);
        $this->logDebug("Request data: " . json_encode($data, JSON_PRETTY_PRINT));

        $attempt = 0;
        while ($attempt < $retries) {
            $attempt++;
            $this->logDebug("Attempt {$attempt} of {$retries}");

            $ch = curl_init();

            // Enhanced cURL options for better reliability
            curl_setopt_array($ch, [
                CURLOPT_URL => $url,
                CURLOPT_POST => true,
                CURLOPT_POSTFIELDS => json_encode($data),
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_HTTPHEADER => [
                    'Content-Type: application/json',
                    'User-Agent: SIHADIR-RPP-System/2.8.0'
                ],
                CURLOPT_TIMEOUT => 180, // 3 minutes timeout
                CURLOPT_CONNECTTIMEOUT => 30, // 30 seconds connection timeout
                CURLOPT_SSL_VERIFYPEER => false,
                CURLOPT_SSL_VERIFYHOST => false,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_MAXREDIRS => 3,
                CURLOPT_ENCODING => '', // Accept all encodings
                CURLOPT_USERAGENT => 'SIHADIR-RPP-System/2.8.0',
                CURLOPT_VERBOSE => $this->debug_enabled
            ]);

            $response = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $curl_error = curl_error($ch);
            $curl_errno = curl_errno($ch);

            // Get additional connection info for debugging
            $connect_time = curl_getinfo($ch, CURLINFO_CONNECT_TIME);
            $total_time = curl_getinfo($ch, CURLINFO_TOTAL_TIME);

            curl_close($ch);

            // Log connection details
            $this->logDebug("Connection time: {$connect_time}s, Total time: {$total_time}s");
            $this->logDebug("HTTP Code: " . $http_code);

            // Handle cURL errors
            if ($curl_error) {
                $this->logDebug("cURL Error (errno: {$curl_errno}): " . $curl_error);

                if ($attempt < $retries) {
                    $this->logDebug("Retrying in 2 seconds...");
                    sleep(2);
                    continue;
                } else {
                    throw new Exception("Koneksi ke API Gemini gagal setelah {$retries} percobaan. Error: " . $curl_error);
                }
            }

            // Log response for debugging
            $this->logDebug("Raw API Response: " . substr($response, 0, 1000) . (strlen($response) > 1000 ? '...[truncated]' : ''));

            // Handle HTTP errors
            if ($http_code !== 200) {
                $error_data = json_decode($response, true);
                $error_message = isset($error_data['error']['message']) ? $error_data['error']['message'] : 'Unknown error';
                $this->logDebug("API Error: " . $error_message);

                // Retry on certain HTTP errors
                if (in_array($http_code, [429, 500, 502, 503, 504]) && $attempt < $retries) {
                    $this->logDebug("Retrying due to HTTP {$http_code} error...");
                    sleep(2);
                    continue;
                } else {
                    throw new Exception("API Gemini mengembalikan error (HTTP {$http_code}): {$error_message}");
                }
            }

            // Success - return response
            $this->logDebug("API request successful on attempt {$attempt}");
            return $response;
        }

        throw new Exception("Gagal menghubungi API Gemini setelah {$retries} percobaan");
    }

    private function parseResponse($response, $config) {
        try {
            // First, decode the API response
            $data = json_decode($response, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                $this->logDebug("Failed to decode API response JSON: " . json_last_error_msg());
                $this->logDebug("Raw response: " . $response);
                throw new Exception("Invalid API response format: " . json_last_error_msg());
            }

            if (!isset($data['candidates'][0]['content']['parts'][0]['text'])) {
                $this->logDebug("Missing expected structure in API response");
                $this->logDebug("Response structure: " . json_encode($data, JSON_PRETTY_PRINT));
                throw new Exception("Format response API tidak valid - missing content text");
            }

            $content = $data['candidates'][0]['content']['parts'][0]['text'];
            $this->logDebug("Extracted content from API: " . $content);

            // Clean and extract JSON from content
            $cleaned_content = $this->cleanAndExtractJson($content);
            $this->logDebug("Cleaned content: " . $cleaned_content);

            $questions_data = json_decode($cleaned_content, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                $this->logDebug("JSON Parse Error: " . json_last_error_msg());
                $this->logDebug("Cleaned content that failed to parse: " . $cleaned_content);

                // Try alternative parsing methods
                $questions_data = $this->tryAlternativeParsing($cleaned_content);

                if (!$questions_data) {
                    throw new Exception("Gagal parsing JSON response: " . json_last_error_msg() . ". Content: " . substr($cleaned_content, 0, 200) . "...");
                }
            }

            if (!isset($questions_data['questions']) || !is_array($questions_data['questions'])) {
                $this->logDebug("Invalid questions structure in parsed data");
                $this->logDebug("Parsed data: " . json_encode($questions_data, JSON_PRETTY_PRINT));
                throw new Exception("Format questions tidak valid dalam response. Expected 'questions' array not found.");
            }

            $this->logDebug("Successfully parsed " . count($questions_data['questions']) . " questions");
            return $this->validateAndFormatQuestions($questions_data['questions'], $config);

        } catch (Exception $e) {
            $this->logDebug("Exception in parseResponse: " . $e->getMessage());
            throw $e;
        }
    }

    private function validateAndFormatQuestions($questions, $config) {
        $formatted_questions = [];
        
        foreach ($questions as $question) {
            if (!isset($question['type']) || !isset($question['question']) || !isset($question['difficulty'])) {
                continue; // Skip invalid questions
            }

            $formatted_question = [
                'question_text' => $question['question'],
                'question_type' => $question['type'],
                'difficulty_level' => $question['difficulty'],
                'options' => null,
                'correct_answer' => null,
                'category' => 'Generated'
            ];

            if ($question['type'] === 'multiple_choice') {
                // Check for incomplete question (ends with ... and no options)
                if (!isset($question['options']) &&
                    (substr(trim($question['question']), -3) === '...' ||
                     substr(trim($question['question']), -1) === '…')) {
                    // Skip incomplete multiple choice questions
                    $this->logDebug("Skipping incomplete multiple choice question: " . substr($question['question'], 0, 50) . "...");
                    continue;
                }

                if (isset($question['options']) && isset($question['correct_answer'])) {
                    // Validate options array
                    $options = $question['options'];
                    if (is_array($options) && count($options) >= 2) {
                        // Filter out empty options
                        $valid_options = array_filter($options, function($option) {
                            return !empty(trim($option));
                        });

                        if (count($valid_options) >= 2) {
                            // Store options as array, not JSON string - database will handle JSON conversion
                            $formatted_question['options'] = array_values($valid_options); // Re-index array
                            $formatted_question['correct_answer'] = $question['correct_answer'];
                        } else {
                            // Skip questions with insufficient valid options
                            $this->logDebug("Skipping question with insufficient options: " . substr($question['question'], 0, 50) . "...");
                            continue;
                        }
                    } else {
                        // Skip questions with invalid options format
                        $this->logDebug("Skipping question with invalid options format: " . substr($question['question'], 0, 50) . "...");
                        continue;
                    }
                } else {
                    // Skip multiple choice questions without options or correct answer
                    $this->logDebug("Skipping multiple choice question missing options/answer: " . substr($question['question'], 0, 50) . "...");
                    continue;
                }
            }

            $formatted_questions[] = $formatted_question;
        }

        return $formatted_questions;
    }

    public function generateRppContent($tema_subtema, $materi_pokok, $mapel_name = '', $kelas_name = '', $kompetensi_dasar = '', $kegiatan_count = 1) {
        if (empty($this->api_key)) {
            throw new Exception("Gemini API key tidak ditemukan. Silakan konfigurasi API key terlebih dahulu.");
        }

        $prompt = $this->buildRppPrompt($tema_subtema, $materi_pokok, $mapel_name, $kelas_name, $kompetensi_dasar, $kegiatan_count);

        $data = [
            'contents' => [
                [
                    'parts' => [
                        ['text' => $prompt]
                    ]
                ]
            ],
            'generationConfig' => [
                'temperature' => 0.7,
                'topK' => 40,
                'topP' => 0.95,
                'maxOutputTokens' => 4096,
            ]
        ];

        $response = $this->makeRequest($data);

        if (!$response) {
            throw new Exception("Gagal menghubungi API Gemini");
        }

        return $this->parseRppResponse($response, $kegiatan_count);
    }

    // Multi-RPP Question Generation Methods
    public function generateMultiRppQuestions($rpp_data_array, $config) {
        if (empty($this->api_key)) {
            throw new Exception("Gemini API key tidak ditemukan. Silakan konfigurasi API key terlebih dahulu.");
        }

        $prompt = $this->buildMultiRppPrompt($rpp_data_array, $config);

        $data = [
            'contents' => [
                [
                    'parts' => [
                        ['text' => $prompt]
                    ]
                ]
            ],
            'generationConfig' => [
                'temperature' => 0.8,
                'topK' => 40,
                'topP' => 0.95,
                'maxOutputTokens' => 8192, // Increased for multi-RPP
            ]
        ];

        $response = $this->makeRequest($data);

        if (!$response) {
            throw new Exception("Gagal menghubungi API Gemini");
        }

        return $this->parseMultiRppResponse($response, $config, $rpp_data_array);
    }

    public function generateMultiRppBlueprint($exam_data, $rpp_data_array, $questions_data, $config) {
        if (empty($this->api_key)) {
            throw new Exception("Gemini API key tidak ditemukan. Silakan konfigurasi API key terlebih dahulu.");
        }

        $prompt = $this->buildMultiRppBlueprintPrompt($exam_data, $rpp_data_array, $questions_data, $config);

        $data = [
            'contents' => [
                [
                    'parts' => [
                        ['text' => $prompt]
                    ]
                ]
            ],
            'generationConfig' => [
                'temperature' => 0.7,
                'topK' => 40,
                'topP' => 0.95,
                'maxOutputTokens' => 6144,
            ]
        ];

        $response = $this->makeRequest($data);

        if (!$response) {
            throw new Exception("Gagal menghubungi API Gemini");
        }

        return $this->parseMultiRppBlueprintResponse($response, $exam_data, $rpp_data_array, $questions_data);
    }

    private function buildMultiRppPrompt($rpp_data_array, $config) {
        $prompt = "Sebagai ahli pendidikan Indonesia, buatlah soal ujian komprehensif yang menggabungkan materi dari beberapa RPP/chapter berikut:\n\n";

        // Add RPP information
        foreach ($rpp_data_array as $index => $rpp) {
            $chapter_num = $index + 1;
            $prompt .= "=== CHAPTER {$chapter_num}: {$rpp['tema_subtema']} ===\n";
            $prompt .= "Mata Pelajaran: {$rpp['nama_mapel']}\n";
            $prompt .= "Kelas: {$rpp['nama_kelas']}\n";
            $prompt .= "Materi Pokok: {$rpp['materi_pokok']}\n";
            $prompt .= "Tujuan Pembelajaran: {$rpp['tujuan_pembelajaran']}\n";
            $prompt .= "Kompetensi Dasar: {$rpp['kompetensi_dasar']}\n";
            $prompt .= "Indikator Pencapaian: {$rpp['indikator_pencapaian']}\n\n";
        }

        $prompt .= "KONFIGURASI SOAL:\n";
        $prompt .= "- Total Soal Pilihan Ganda: {$config['multiple_choice_count']}\n";
        $prompt .= "- Total Soal Essay: {$config['essay_count']}\n";
        $prompt .= "- Opsi Pilihan Ganda: {$config['multiple_choice_options']} pilihan\n";
        $prompt .= "- Distribusi Kesulitan Regular: {$config['regular_count']}\n";
        $prompt .= "- Distribusi Kesulitan HOTS Mudah: {$config['hots_easy_count']}\n";
        $prompt .= "- Distribusi Kesulitan HOTS Sedang: {$config['hots_medium_count']}\n";
        $prompt .= "- Distribusi Kesulitan HOTS Tinggi: {$config['hots_hard_count']}\n\n";

        $prompt .= "INSTRUKSI KHUSUS:\n";
        $prompt .= "1. Distribusikan soal secara PROPORSIONAL dari setiap chapter\n";
        $prompt .= "2. Buat soal yang menghubungkan konsep antar chapter jika memungkinkan\n";
        $prompt .= "3. Pastikan setiap chapter terwakili minimal 1 soal\n";
        $prompt .= "4. Gunakan level kognitif Bloom's Taxonomy (C1-C6)\n";
        $prompt .= "5. Untuk soal HOTS, gunakan kata kerja operasional tingkat tinggi\n";
        $prompt .= "6. Hindari kata kerja 'jelaskan', 'analisis', 'evaluasi', 'simpulkan' untuk pilihan ganda\n";
        $prompt .= "7. Gunakan kata kerja yang kompatibel dengan pilihan ganda: 'bandingkan', 'kategorikan', 'tentukan', 'pilih'\n";
        $prompt .= "8. PENTING: Untuk soal essay, JANGAN sertakan penjelasan jawaban atau kunci jawaban\n";
        $prompt .= "9. Soal essay hanya berisi pertanyaan saja, tanpa jawaban atau penjelasan\n\n";

        $prompt .= "FORMAT OUTPUT (JSON):\n";
        $prompt .= "{\n";
        $prompt .= "  \"questions\": [\n";
        $prompt .= "    // Untuk soal pilihan ganda:\n";
        $prompt .= "    {\n";
        $prompt .= "      \"source_chapter\": 1,\n";
        $prompt .= "      \"source_rpp_id\": [ID_RPP],\n";
        $prompt .= "      \"question_text\": \"[TEKS_SOAL]\",\n";
        $prompt .= "      \"question_type\": \"multiple_choice\",\n";
        $prompt .= "      \"options\": [\"A. ...\", \"B. ...\", \"C. ...\", \"D. ...\"],\n";
        $prompt .= "      \"correct_answer\": \"A\",\n";
        $prompt .= "      \"difficulty_level\": \"regular|hots_easy|hots_medium|hots_hard\",\n";
        $prompt .= "      \"cognitive_level\": \"C1|C2|C3|C4|C5|C6\",\n";
        $prompt .= "      \"explanation\": \"[PENJELASAN_JAWABAN]\"\n";
        $prompt .= "    },\n";
        $prompt .= "    // Untuk soal essay (TANPA explanation):\n";
        $prompt .= "    {\n";
        $prompt .= "      \"source_chapter\": 2,\n";
        $prompt .= "      \"source_rpp_id\": [ID_RPP],\n";
        $prompt .= "      \"question_text\": \"[TEKS_SOAL_ESSAY]\",\n";
        $prompt .= "      \"question_type\": \"essay\",\n";
        $prompt .= "      \"difficulty_level\": \"regular|hots_easy|hots_medium|hots_hard\",\n";
        $prompt .= "      \"cognitive_level\": \"C1|C2|C3|C4|C5|C6\"\n";
        $prompt .= "    }\n";
        $prompt .= "  ]\n";
        $prompt .= "}\n\n";

        $prompt .= "Buatlah soal yang berkualitas, relevan dengan materi, dan sesuai dengan tingkat kesulitan yang diminta!";

        return $prompt;
    }

    private function buildMultiRppBlueprintPrompt($exam_data, $rpp_data_array, $questions_data, $config) {
        $prompt = "Sebagai ahli pendidikan Indonesia, buatlah kisi-kisi ujian (exam blueprint) komprehensif untuk ujian multi-chapter berikut:\n\n";

        $prompt .= "=== INFORMASI UJIAN ===\n";
        $prompt .= "Judul Ujian: {$exam_data['exam_title']}\n";
        $prompt .= "Jenis Ujian: {$exam_data['exam_type']}\n";
        $prompt .= "Semester: {$exam_data['semester']}\n";
        $prompt .= "Tahun Ajaran: {$exam_data['tahun_ajaran']}\n";
        $prompt .= "Durasi: {$exam_data['exam_duration']} menit\n";
        $prompt .= "Total Skor: {$exam_data['total_score']}\n\n";

        $prompt .= "=== CHAPTER/MATERI YANG DIUJIKAN ===\n";
        foreach ($rpp_data_array as $index => $rpp) {
            $chapter_num = $index + 1;
            $prompt .= "Chapter {$chapter_num}: {$rpp['tema_subtema']}\n";
            $prompt .= "- Mata Pelajaran: {$rpp['nama_mapel']}\n";
            $prompt .= "- Materi Pokok: {$rpp['materi_pokok']}\n";
            $prompt .= "- Tujuan Pembelajaran: {$rpp['tujuan_pembelajaran']}\n";
            $prompt .= "- Kompetensi Dasar: {$rpp['kompetensi_dasar']}\n\n";
        }

        // Analyze questions distribution
        $question_stats = $this->analyzeMultiRppQuestions($questions_data);
        $prompt .= "=== DISTRIBUSI SOAL ===\n";
        $prompt .= "Total Soal: {$question_stats['total']}\n";
        $prompt .= "Pilihan Ganda: {$question_stats['multiple_choice']}\n";
        $prompt .= "Essay: {$question_stats['essay']}\n";
        foreach ($question_stats['by_chapter'] as $chapter => $count) {
            $prompt .= "Chapter {$chapter}: {$count} soal\n";
        }
        $prompt .= "\n";

        $prompt .= "FORMAT OUTPUT (JSON):\n";
        $prompt .= "{\n";
        $prompt .= "  \"exam_info\": {\n";
        $prompt .= "    \"title\": \"[JUDUL_UJIAN]\",\n";
        $prompt .= "    \"type\": \"[JENIS_UJIAN]\",\n";
        $prompt .= "    \"subject\": \"[MATA_PELAJARAN]\",\n";
        $prompt .= "    \"duration\": \"[DURASI]\",\n";
        $prompt .= "    \"total_score\": \"[TOTAL_SKOR]\"\n";
        $prompt .= "  },\n";
        $prompt .= "  \"learning_objectives\": [\n";
        $prompt .= "    {\n";
        $prompt .= "      \"chapter\": \"[NAMA_CHAPTER]\",\n";
        $prompt .= "      \"objectives\": [\"[TUJUAN_1]\", \"[TUJUAN_2]\"]\n";
        $prompt .= "    }\n";
        $prompt .= "  ],\n";
        $prompt .= "  \"question_distribution\": {\n";
        $prompt .= "    \"by_chapter\": {\"Chapter 1\": 5, \"Chapter 2\": 3},\n";
        $prompt .= "    \"by_type\": {\"multiple_choice\": 8, \"essay\": 2},\n";
        $prompt .= "    \"by_difficulty\": {\"regular\": 6, \"hots\": 4}\n";
        $prompt .= "  },\n";
        $prompt .= "  \"cognitive_mapping\": {\n";
        $prompt .= "    \"C1\": 2, \"C2\": 2, \"C3\": 2, \"C4\": 2, \"C5\": 1, \"C6\": 1\n";
        $prompt .= "  },\n";
        $prompt .= "  \"blueprint_table\": [\n";
        $prompt .= "    {\n";
        $prompt .= "      \"chapter\": \"[NAMA_CHAPTER]\",\n";
        $prompt .= "      \"learning_objective\": \"[TUJUAN_PEMBELAJARAN]\",\n";
        $prompt .= "      \"indicator\": \"[INDIKATOR]\",\n";
        $prompt .= "      \"cognitive_level\": \"C1-C6\",\n";
        $prompt .= "      \"question_numbers\": [1, 2, 3],\n";
        $prompt .= "      \"total_questions\": 3\n";
        $prompt .= "    }\n";
        $prompt .= "  ]\n";
        $prompt .= "}\n\n";

        $prompt .= "Buatlah kisi-kisi yang komprehensif, terstruktur, dan sesuai dengan standar pendidikan Indonesia!";

        return $prompt;
    }

    private function buildRppPrompt($tema_subtema, $materi_pokok, $mapel_name, $kelas_name, $kompetensi_dasar = '', $kegiatan_count = 1) {
        $prompt = "Sebagai ahli pendidikan Indonesia, buatlah konten RPP (Rencana Pelaksanaan Pembelajaran) yang lengkap dan sesuai dengan kurikulum Indonesia.\n\n";

        $prompt .= "=== INFORMASI DASAR ===\n";
        if (!empty($mapel_name)) {
            $prompt .= "Mata Pelajaran: " . $mapel_name . "\n";
        }
        if (!empty($kelas_name)) {
            $prompt .= "Kelas: " . $kelas_name . "\n";
        }
        $prompt .= "Tema/Subtema: " . $tema_subtema . "\n";
        $prompt .= "Materi Pokok: " . $materi_pokok . "\n";

        // Add existing Kompetensi Dasar if provided
        if (!empty($kompetensi_dasar)) {
            $prompt .= "Kompetensi Dasar (sudah ditentukan): " . $kompetensi_dasar . "\n";
        }
        $prompt .= "\n";

        $prompt .= "=== TUGAS ===\n";
        if (!empty($kompetensi_dasar)) {
            $prompt .= "Buatlah konten RPP berdasarkan Kompetensi Dasar yang sudah ditentukan di atas. Konten yang perlu dibuat:\n";
            $prompt .= "1. Tujuan Pembelajaran (4-6 poin dengan kata kerja operasional, selaras dengan KD)\n";
            $prompt .= "2. Indikator Pencapaian (terukur dan spesifik, turunan dari KD)\n";
            $prompt .= "3. Materi Pembelajaran (outline materi sesuai KD dan tema)\n";
            $prompt .= "4. Metode Pembelajaran (variasi metode yang sesuai)\n";
            $prompt .= "5. Media Pembelajaran (media dan alat yang digunakan)\n";
            $prompt .= "6. Sumber Belajar (buku, modul, referensi)\n";
            $prompt .= "7. Penilaian (aspek sikap, pengetahuan, keterampilan)\n";
            $prompt .= "8. Kegiatan Pembelajaran:\n";
            if ($kegiatan_count > 1) {
                $prompt .= "   PENTING: Buatlah {$kegiatan_count} set kegiatan pembelajaran yang BERBEDA dan PROGRESIF.\n";
                $prompt .= "   Setiap kegiatan harus memiliki konten yang UNIK dan saling melengkapi.\n";
                for ($i = 1; $i <= $kegiatan_count; $i++) {
                    $prompt .= "   Kegiatan {$i}:\n";
                    $prompt .= "   - Pendahuluan (pembukaan, apersepsi, motivasi)\n";
                    $prompt .= "   - Kegiatan Inti (eksplorasi, elaborasi, konfirmasi)\n";
                    $prompt .= "   - Penutup (kesimpulan, refleksi, tindak lanjut)\n";
                }
                $prompt .= "\n";
            } else {
                $prompt .= "   - Pendahuluan (pembukaan, apersepsi, motivasi)\n";
                $prompt .= "   - Kegiatan Inti (eksplorasi, elaborasi, konfirmasi)\n";
                $prompt .= "   - Penutup (kesimpulan, refleksi, tindak lanjut)\n\n";
            }
            $prompt .= "PENTING: Pastikan semua konten yang dibuat selaras dan mendukung pencapaian Kompetensi Dasar yang sudah ditentukan.\n\n";
        } else {
            $prompt .= "Buatlah konten RPP yang mencakup:\n";
            $prompt .= "1. Tujuan Pembelajaran (4-6 poin dengan kata kerja operasional)\n";
            $prompt .= "2. Kompetensi Dasar (sesuai kurikulum Indonesia)\n";
            $prompt .= "3. Indikator Pencapaian (terukur dan spesifik)\n";
            $prompt .= "4. Materi Pembelajaran (outline materi yang akan diajarkan)\n";
            $prompt .= "5. Metode Pembelajaran (variasi metode yang sesuai)\n";
            $prompt .= "6. Media Pembelajaran (media dan alat yang digunakan)\n";
            $prompt .= "7. Sumber Belajar (buku, modul, referensi)\n";
            $prompt .= "8. Penilaian (aspek sikap, pengetahuan, keterampilan)\n";
            $prompt .= "9. Kegiatan Pembelajaran:\n";
            if ($kegiatan_count > 1) {
                $prompt .= "   PENTING: Buatlah {$kegiatan_count} set kegiatan pembelajaran yang BERBEDA dan PROGRESIF.\n";
                $prompt .= "   Setiap kegiatan harus memiliki konten yang UNIK dan saling melengkapi.\n";
                for ($i = 1; $i <= $kegiatan_count; $i++) {
                    $prompt .= "   Kegiatan {$i}:\n";
                    $prompt .= "   - Pendahuluan (pembukaan, apersepsi, motivasi)\n";
                    $prompt .= "   - Kegiatan Inti (eksplorasi, elaborasi, konfirmasi)\n";
                    $prompt .= "   - Penutup (kesimpulan, refleksi, tindak lanjut)\n";
                }
                $prompt .= "\n";
            } else {
                $prompt .= "   - Pendahuluan (pembukaan, apersepsi, motivasi)\n";
                $prompt .= "   - Kegiatan Inti (eksplorasi, elaborasi, konfirmasi)\n";
                $prompt .= "   - Penutup (kesimpulan, refleksi, tindak lanjut)\n\n";
            }
        }

        $prompt .= "=== FORMAT OUTPUT ===\n";
        $prompt .= "Berikan output dalam format JSON yang valid:\n\n";
        $prompt .= "```json\n";
        $prompt .= "{\n";
        $prompt .= "  \"tujuan_pembelajaran\": \"1. Siswa dapat...\n2. Siswa mampu...\",\n";

        // Only include kompetensi_dasar in output if it wasn't provided as input
        if (empty($kompetensi_dasar)) {
            $prompt .= "  \"kompetensi_dasar\": \"3.1 Memahami...\n4.1 Menerapkan...\",\n";
        }

        $prompt .= "  \"indikator_pencapaian\": \"3.1.1 Menjelaskan...\n3.1.2 Mengidentifikasi...\",\n";
        $prompt .= "  \"materi_pembelajaran\": \"1. Konsep dasar...\n2. Prinsip-prinsip...\",\n";
        $prompt .= "  \"metode_pembelajaran\": \"1. Ceramah interaktif\n2. Diskusi kelompok\",\n";
        $prompt .= "  \"media_pembelajaran\": \"1. LCD Proyektor\n2. Komputer/Laptop\",\n";
        $prompt .= "  \"sumber_belajar\": \"1. Buku teks...\n2. Modul pembelajaran...\",\n";
        $prompt .= "  \"penilaian\": \"1. Sikap: Observasi...\n2. Pengetahuan: Tes tertulis...\",\n";

        if ($kegiatan_count > 1) {
            $prompt .= "  \"kegiatan_pembelajaran\": [\n";
            for ($i = 1; $i <= $kegiatan_count; $i++) {
                $prompt .= "    {\n";
                $prompt .= "      \"kegiatan_ke\": {$i},\n";
                $prompt .= "      \"pendahuluan\": \"Kegiatan {$i} - Pendahuluan yang unik...\",\n";
                $prompt .= "      \"kegiatan_inti\": \"Kegiatan {$i} - Inti yang berbeda...\",\n";
                $prompt .= "      \"penutup\": \"Kegiatan {$i} - Penutup yang spesifik...\"\n";
                $prompt .= "    }";
                if ($i < $kegiatan_count) {
                    $prompt .= ",";
                }
                $prompt .= "\n";
            }
            $prompt .= "  ]\n";
        } else {
            $prompt .= "  \"kegiatan_pendahuluan\": \"1. Guru membuka pelajaran...\n2. Guru menyampaikan tujuan...\",\n";
            $prompt .= "  \"kegiatan_inti\": \"1. Guru menjelaskan konsep...\n2. Siswa mengamati...\",\n";
            $prompt .= "  \"kegiatan_penutup\": \"1. Siswa menyimpulkan...\n2. Guru memberikan evaluasi...\"\n";
        }
        $prompt .= "}\n";
        $prompt .= "```\n\n";

        $prompt .= "=== PEDOMAN PENTING ===\n";
        $prompt .= "1. Gunakan bahasa Indonesia yang baik dan benar\n";
        $prompt .= "2. Sesuaikan dengan kurikulum dan standar pendidikan Indonesia\n";
        $prompt .= "3. Gunakan kata kerja operasional yang terukur (Taksonomi Bloom)\n";
        $prompt .= "4. Pastikan kesesuaian antara tujuan, indikator, dan penilaian\n";
        $prompt .= "5. Berikan konten yang praktis dan dapat diterapkan\n";
        $prompt .= "6. Setiap poin diberi nomor dan penjelasan yang jelas\n";
        if ($kegiatan_count > 1) {
            $prompt .= "7. KHUSUS MULTIPLE KEGIATAN: Pastikan setiap kegiatan memiliki konten yang BENAR-BENAR BERBEDA:\n";
            $prompt .= "   - Kegiatan 1: Pengenalan dan dasar-dasar\n";
            $prompt .= "   - Kegiatan 2: Pendalaman dan praktik\n";
            $prompt .= "   - Kegiatan 3: Aplikasi dan evaluasi\n";
            $prompt .= "   - Kegiatan 4: Sintesis dan refleksi\n";
            $prompt .= "   - Gunakan pendekatan, metode, dan aktivitas yang BERBEDA untuk setiap kegiatan\n";
            $prompt .= "   - Hindari pengulangan konten yang sama\n";
        }
        $prompt .= "8. Berikan HANYA JSON yang valid, tanpa teks tambahan\n";

        return $prompt;
    }

    private function parseRppResponse($response, $kegiatan_count = 1) {
        $data = json_decode($response, true);

        if (!isset($data['candidates'][0]['content']['parts'][0]['text'])) {
            throw new Exception("Format response API tidak valid");
        }

        $content = $data['candidates'][0]['content']['parts'][0]['text'];

        // Clean up the content to extract JSON
        $content = trim($content);

        // Remove markdown code blocks
        $content = preg_replace('/^```json\s*/m', '', $content);
        $content = preg_replace('/^```\s*/m', '', $content);
        $content = preg_replace('/\s*```$/m', '', $content);

        // Try to find JSON content between curly braces
        if (preg_match('/\{.*\}/s', $content, $matches)) {
            $content = $matches[0];
        }

        $rpp_data = json_decode($content, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            // Log the content for debugging
            error_log("RPP JSON Parse Error: " . json_last_error_msg());
            error_log("Content: " . $content);
            throw new Exception("Gagal parsing JSON response: " . json_last_error_msg() . ". Silakan coba lagi.");
        }

        // Validate required fields (kompetensi_dasar is optional if provided as input)
        $required_fields = [
            'tujuan_pembelajaran', 'indikator_pencapaian',
            'materi_pembelajaran', 'metode_pembelajaran', 'media_pembelajaran',
            'sumber_belajar', 'penilaian'
        ];

        // Add kompetensi_dasar to required fields only if it should be generated
        if (!isset($rpp_data['kompetensi_dasar'])) {
            // If kompetensi_dasar is not in response, it means it was provided as input
            // This is acceptable, so we don't add it to required fields
        } else {
            // If kompetensi_dasar is in response, validate it
            $required_fields[] = 'kompetensi_dasar';
        }

        // Handle different kegiatan formats
        if ($kegiatan_count > 1) {
            // Multiple activities format
            if (!isset($rpp_data['kegiatan_pembelajaran']) || !is_array($rpp_data['kegiatan_pembelajaran'])) {
                throw new Exception("Field 'kegiatan_pembelajaran' array tidak ditemukan dalam response untuk multiple activities. Silakan coba lagi.");
            }

            // Convert array format to individual fields for backward compatibility
            $kegiatan_data = $rpp_data['kegiatan_pembelajaran'];
            $rpp_data['kegiatan_pendahuluan'] = [];
            $rpp_data['kegiatan_inti'] = [];
            $rpp_data['kegiatan_penutup'] = [];

            foreach ($kegiatan_data as $kegiatan) {
                if (isset($kegiatan['pendahuluan'])) {
                    $rpp_data['kegiatan_pendahuluan'][] = $kegiatan['pendahuluan'];
                }
                if (isset($kegiatan['kegiatan_inti'])) {
                    $rpp_data['kegiatan_inti'][] = $kegiatan['kegiatan_inti'];
                }
                if (isset($kegiatan['penutup'])) {
                    $rpp_data['kegiatan_penutup'][] = $kegiatan['penutup'];
                }
            }

            // Remove the array format to avoid confusion
            unset($rpp_data['kegiatan_pembelajaran']);
        } else {
            // Single activity format
            $required_fields = array_merge($required_fields, [
                'kegiatan_pendahuluan', 'kegiatan_inti', 'kegiatan_penutup'
            ]);
        }

        foreach ($required_fields as $field) {
            if (!isset($rpp_data[$field])) {
                throw new Exception("Field '$field' tidak ditemukan dalam response. Silakan coba lagi.");
            }
        }

        // Sanitize and validate data types to prevent frontend errors
        $sanitized_data = $this->sanitizeRppData($rpp_data);

        return $sanitized_data;
    }

    /**
     * Sanitize RPP data to ensure all values are properly formatted for frontend consumption
     */
    private function sanitizeRppData($rpp_data) {
        $sanitized = [];

        // List of fields that should be strings
        $string_fields = [
            'tujuan_pembelajaran', 'kompetensi_dasar', 'indikator_pencapaian',
            'materi_pembelajaran', 'metode_pembelajaran', 'media_pembelajaran',
            'sumber_belajar', 'penilaian', 'kegiatan_pendahuluan', 'kegiatan_inti', 'kegiatan_penutup'
        ];

        foreach ($rpp_data as $key => $value) {
            if (in_array($key, $string_fields)) {
                // Ensure string fields are actually strings
                if (is_array($value)) {
                    // If it's an array, keep it as array (for kegiatan fields)
                    $sanitized[$key] = array_map(function($item) {
                        return is_string($item) ? $item : (string)$item;
                    }, $value);
                } else {
                    // Convert to string if not already
                    $sanitized[$key] = is_string($value) ? $value : (string)$value;
                }
            } else {
                // For other fields, keep as is but ensure not null
                $sanitized[$key] = $value !== null ? $value : '';
            }
        }

        return $sanitized;
    }

    public function analyzeQuestionDifficulty($question_text, $question_type, $rpp_context = '') {
        if (empty($this->api_key)) {
            throw new Exception("Gemini API key tidak ditemukan. Silakan konfigurasi API key terlebih dahulu.");
        }

        $prompt = $this->buildAnalysisPrompt($question_text, $question_type, $rpp_context);

        $data = [
            'contents' => [
                [
                    'parts' => [
                        ['text' => $prompt]
                    ]
                ]
            ],
            'generationConfig' => [
                'temperature' => 0.3,
                'topK' => 40,
                'topP' => 0.95,
                'maxOutputTokens' => 2048,
            ]
        ];

        $response = $this->makeRequest($data);

        if (!$response) {
            throw new Exception("Gagal menghubungi API Gemini");
        }

        return $this->parseAnalysisResponse($response);
    }

    public function enhanceQuestion($question_text, $question_type, $enhancement_type, $rpp_context = '') {
        if (empty($this->api_key)) {
            throw new Exception("Gemini API key tidak ditemukan. Silakan konfigurasi API key terlebih dahulu.");
        }

        $prompt = $this->buildEnhancementPrompt($question_text, $question_type, $enhancement_type, $rpp_context);

        $data = [
            'contents' => [
                [
                    'parts' => [
                        ['text' => $prompt]
                    ]
                ]
            ],
            'generationConfig' => [
                'temperature' => 0.7,
                'topK' => 40,
                'topP' => 0.95,
                'maxOutputTokens' => 2048,
            ]
        ];

        $response = $this->makeRequest($data);

        if (!$response) {
            throw new Exception("Gagal menghubungi API Gemini");
        }

        return $this->parseEnhancementResponse($response);
    }

    private function buildAnalysisPrompt($question_text, $question_type, $rpp_context) {
        $prompt = "Sebagai ahli pendidikan, analisis tingkat kesulitan soal berikut:\n\n";

        if (!empty($rpp_context)) {
            $prompt .= "=== KONTEKS RPP ===\n";
            $prompt .= $rpp_context . "\n\n";
        }

        $prompt .= "=== SOAL YANG DIANALISIS ===\n";
        $prompt .= "Jenis: " . ($question_type === 'multiple_choice' ? 'Pilihan Ganda' : 'Essay') . "\n";
        $prompt .= "Soal: " . $question_text . "\n\n";

        $prompt .= "=== TUGAS ANALISIS ===\n";
        $prompt .= "Analisis soal berdasarkan:\n";
        $prompt .= "1. Kata kerja operasional yang digunakan\n";
        $prompt .= "2. Tingkat kognitif (Taksonomi Bloom)\n";
        $prompt .= "3. Kompleksitas pemikiran yang diperlukan\n";
        $prompt .= "4. Apakah soal memerlukan analisis, evaluasi, atau kreasi\n";
        $prompt .= "5. VALIDASI: Apakah kata kerja sesuai dengan jenis soal?\n";
        $prompt .= "   - Pilihan ganda dengan 'jelaskan/analisis/evaluasi' = TIDAK SESUAI\n";
        $prompt .= "   - Essay dengan kata kerja pilihan = KURANG OPTIMAL\n\n";

        $prompt .= "=== FORMAT OUTPUT ===\n";
        $prompt .= "Berikan output dalam format JSON:\n\n";
        $prompt .= "```json\n";
        $prompt .= "{\n";
        $prompt .= "  \"difficulty_level\": \"regular|hots_easy|hots_medium|hots_hard\",\n";
        $prompt .= "  \"cognitive_level\": \"C1-C6\",\n";
        $prompt .= "  \"reasoning\": \"Penjelasan mengapa soal dikategorikan pada tingkat ini\",\n";
        $prompt .= "  \"keywords\": [\"kata kunci yang menunjukkan tingkat kesulitan\"],\n";
        $prompt .= "  \"is_hots\": true/false,\n";
        $prompt .= "  \"confidence_score\": 0.0-1.0,\n";
        $prompt .= "  \"verb_appropriateness\": {\n";
        $prompt .= "    \"is_appropriate\": true/false,\n";
        $prompt .= "    \"issue\": \"Deskripsi masalah jika tidak sesuai\",\n";
        $prompt .= "    \"suggestion\": \"Saran perbaikan kata kerja\"\n";
        $prompt .= "  }\n";
        $prompt .= "}\n";
        $prompt .= "```\n\n";

        $prompt .= "KRITERIA:\n";
        $prompt .= "- Regular: C1-C3 (mengingat, memahami, menerapkan)\n";
        $prompt .= "- HOTS Easy: C4 (menganalisis) - level dasar\n";
        $prompt .= "- HOTS Medium: C4-C5 (menganalisis-mengevaluasi) - level menengah\n";
        $prompt .= "- HOTS Hard: C5-C6 (mengevaluasi-mencipta) - level tinggi\n";
        $prompt .= "Berikan HANYA JSON yang valid, tanpa teks tambahan.";

        return $prompt;
    }

    private function buildEnhancementPrompt($question_text, $question_type, $enhancement_type, $rpp_context) {
        $prompt = "Sebagai ahli pendidikan, berikan saran perbaikan untuk soal berikut:\n\n";

        if (!empty($rpp_context)) {
            $prompt .= "=== KONTEKS RPP ===\n";
            $prompt .= $rpp_context . "\n\n";
        }

        $prompt .= "=== SOAL ASLI ===\n";
        $prompt .= "Jenis: " . ($question_type === 'multiple_choice' ? 'Pilihan Ganda' : 'Essay') . "\n";
        $prompt .= "Soal: " . $question_text . "\n\n";

        $prompt .= "=== JENIS PERBAIKAN ===\n";
        if ($enhancement_type === 'upgrade_to_hots') {
            $prompt .= "Tingkatkan soal menjadi HOTS (Higher Order Thinking Skills)\n";
            $prompt .= "- Gunakan kata kerja operasional tingkat tinggi\n";
            $prompt .= "- Tambahkan elemen analisis, evaluasi, atau kreasi\n";
            $prompt .= "- Buat soal memerlukan pemikiran kritis\n";
        } elseif ($enhancement_type === 'make_unique') {
            $prompt .= "Buat soal lebih unik dan sulit dicari di internet\n";
            $prompt .= "- Gunakan konteks spesifik atau kasus nyata\n";
            $prompt .= "- Hindari pertanyaan yang mudah di-copy-paste\n";
            $prompt .= "- Buat soal kontekstual dan aplikatif\n";
        } else {
            $prompt .= "Perbaiki kualitas soal secara umum\n";
            $prompt .= "- Perbaiki struktur kalimat\n";
            $prompt .= "- Tingkatkan kejelasan pertanyaan\n";
            $prompt .= "- Sesuaikan dengan tujuan pembelajaran\n";
        }

        $prompt .= "\n=== FORMAT OUTPUT ===\n";
        $prompt .= "Berikan output dalam format JSON:\n\n";
        $prompt .= "```json\n";
        $prompt .= "{\n";
        $prompt .= "  \"enhanced_question\": \"Soal yang telah diperbaiki\",\n";
        $prompt .= "  \"improvements\": [\"Daftar perbaikan yang dilakukan\"],\n";
        $prompt .= "  \"difficulty_level\": \"regular|hots_easy|hots_medium|hots_hard\",\n";
        $prompt .= "  \"reasoning\": \"Penjelasan mengapa perbaikan ini diperlukan\",\n";
        $prompt .= "  \"enhancement_type\": \"" . $enhancement_type . "\"\n";
        $prompt .= "}\n";
        $prompt .= "```\n\n";

        $prompt .= "Berikan HANYA JSON yang valid, tanpa teks tambahan.";

        return $prompt;
    }

    private function parseAnalysisResponse($response) {
        $data = json_decode($response, true);

        if (!isset($data['candidates'][0]['content']['parts'][0]['text'])) {
            throw new Exception("Format response API tidak valid");
        }

        $content = $data['candidates'][0]['content']['parts'][0]['text'];

        // Clean up the content to extract JSON
        $content = trim($content);
        $content = preg_replace('/^```json\s*/m', '', $content);
        $content = preg_replace('/^```\s*/m', '', $content);
        $content = preg_replace('/\s*```$/m', '', $content);

        // Try to find JSON content between curly braces
        if (preg_match('/\{.*\}/s', $content, $matches)) {
            $content = $matches[0];
        }

        $analysis_data = json_decode($content, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            error_log("Analysis JSON Parse Error: " . json_last_error_msg());
            error_log("Content: " . $content);
            throw new Exception("Gagal parsing JSON response: " . json_last_error_msg());
        }

        // Validate required fields
        $required_fields = ['difficulty_level', 'cognitive_level', 'reasoning', 'is_hots', 'confidence_score'];
        foreach ($required_fields as $field) {
            if (!isset($analysis_data[$field])) {
                throw new Exception("Field '$field' tidak ditemukan dalam response analisis.");
            }
        }

        // Validate verb appropriateness if present
        if (isset($analysis_data['verb_appropriateness'])) {
            $verb_check = $analysis_data['verb_appropriateness'];
            if (isset($verb_check['is_appropriate']) && !$verb_check['is_appropriate']) {
                // Add warning to reasoning if verb is inappropriate
                $analysis_data['reasoning'] .= "\n\nPERINGATAN: " . ($verb_check['issue'] ?? 'Kata kerja tidak sesuai dengan jenis soal.');
                if (isset($verb_check['suggestion'])) {
                    $analysis_data['reasoning'] .= "\nSaran: " . $verb_check['suggestion'];
                }
            }
        }

        return $analysis_data;
    }

    private function parseEnhancementResponse($response) {
        $data = json_decode($response, true);

        if (!isset($data['candidates'][0]['content']['parts'][0]['text'])) {
            throw new Exception("Format response API tidak valid");
        }

        $content = $data['candidates'][0]['content']['parts'][0]['text'];

        // Clean up the content to extract JSON
        $content = trim($content);
        $content = preg_replace('/^```json\s*/m', '', $content);
        $content = preg_replace('/^```\s*/m', '', $content);
        $content = preg_replace('/\s*```$/m', '', $content);

        // Try to find JSON content between curly braces
        if (preg_match('/\{.*\}/s', $content, $matches)) {
            $content = $matches[0];
        }

        $enhancement_data = json_decode($content, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            error_log("Enhancement JSON Parse Error: " . json_last_error_msg());
            error_log("Content: " . $content);
            throw new Exception("Gagal parsing JSON response: " . json_last_error_msg());
        }

        // Validate required fields
        $required_fields = ['enhanced_question', 'improvements', 'difficulty_level', 'reasoning'];
        foreach ($required_fields as $field) {
            if (!isset($enhancement_data[$field])) {
                throw new Exception("Field '$field' tidak ditemukan dalam response enhancement.");
            }
        }

        return $enhancement_data;
    }

    public function generateBlueprint($rpp_data, $questions, $config) {
        if (empty($this->api_key)) {
            throw new Exception("Gemini API key tidak ditemukan. Silakan konfigurasi API key terlebih dahulu.");
        }

        $prompt = $this->buildBlueprintPrompt($rpp_data, $questions, $config);

        $data = [
            'contents' => [
                [
                    'parts' => [
                        ['text' => $prompt]
                    ]
                ]
            ],
            'generationConfig' => [
                'temperature' => 0.3,
                'topK' => 40,
                'topP' => 0.95,
                'maxOutputTokens' => 4096,
            ]
        ];

        $response = $this->makeRequest($data);

        if (!$response) {
            throw new Exception("Gagal menghubungi API Gemini");
        }

        return $this->parseBlueprintResponse($response);
    }

    private function buildBlueprintPrompt($rpp_data, $questions, $config) {
        $prompt = "Sebagai ahli pendidikan Indonesia, buatlah kisi-kisi ujian (blueprint) yang komprehensif berdasarkan RPP dan soal-soal yang tersedia.\n\n";

        // RPP Information
        $prompt .= "=== INFORMASI RPP ===\n";
        $prompt .= "Mata Pelajaran: " . $rpp_data['nama_mapel'] . "\n";
        $prompt .= "Kelas: " . $rpp_data['nama_kelas'] . "\n";
        $prompt .= "Tema/Subtema: " . $rpp_data['tema_subtema'] . "\n";
        $prompt .= "Materi Pokok: " . $rpp_data['materi_pokok'] . "\n";
        $prompt .= "Tujuan Pembelajaran: " . $rpp_data['tujuan_pembelajaran'] . "\n";
        $prompt .= "Kompetensi Dasar: " . $rpp_data['kompetensi_dasar'] . "\n";
        $prompt .= "Indikator Pencapaian: " . $rpp_data['indikator_pencapaian'] . "\n\n";

        // Exam Configuration
        $prompt .= "=== KONFIGURASI UJIAN ===\n";
        $prompt .= "Jenis Ujian: " . ucwords(str_replace('_', ' ', $config['exam_type'])) . "\n";
        $prompt .= "Semester: " . $config['semester'] . "\n";
        $prompt .= "Durasi: " . $config['exam_duration'] . " menit\n";
        $prompt .= "Total Skor: " . $config['total_score'] . "\n";
        if (!empty($config['additional_notes'])) {
            $prompt .= "Catatan: " . $config['additional_notes'] . "\n";
        }
        $prompt .= "\n";

        // Questions Analysis
        $stats = $this->analyzeQuestions($questions);
        $prompt .= "=== ANALISIS SOAL TERSEDIA ===\n";
        $prompt .= "Total Soal: " . count($questions) . "\n";
        $prompt .= "Pilihan Ganda: " . $stats['multiple_choice'] . " soal\n";
        $prompt .= "Essay: " . $stats['essay'] . " soal\n";
        $prompt .= "Regular (C1-C3): " . $stats['regular'] . " soal\n";
        $prompt .= "HOTS Mudah (C4): " . $stats['hots_easy'] . " soal\n";
        $prompt .= "HOTS Sedang (C4-C5): " . $stats['hots_medium'] . " soal\n";
        $prompt .= "HOTS Tinggi (C5-C6): " . $stats['hots_hard'] . " soal\n\n";

        // Questions List
        $prompt .= "=== DAFTAR SOAL ===\n";
        foreach ($questions as $index => $question) {
            $prompt .= "Soal " . ($index + 1) . ":\n";
            $prompt .= "- Jenis: " . ($question['question_type'] == 'multiple_choice' ? 'Pilihan Ganda' : 'Essay') . "\n";
            $prompt .= "- Tingkat: " . $this->getDifficultyLabel($question['difficulty_level']) . "\n";
            $prompt .= "- Teks: " . substr($question['question_text'], 0, 100) . "...\n\n";
        }

        // Blueprint Requirements
        $prompt .= "=== TUGAS PEMBUATAN KISI-KISI ===\n";
        $prompt .= "Buatlah kisi-kisi ujian yang mencakup:\n";
        $prompt .= "1. Informasi umum ujian (mata pelajaran, kelas, waktu, dll)\n";
        $prompt .= "2. Tabel pemetaan soal dengan kolom:\n";
        $prompt .= "   - No urut\n";
        $prompt .= "   - Kompetensi Dasar\n";
        $prompt .= "   - Materi\n";
        $prompt .= "   - Indikator Soal\n";
        $prompt .= "   - Level Kognitif (C1-C6)\n";
        $prompt .= "   - Bentuk Soal (PG/Essay)\n";
        $prompt .= "   - No Soal\n";
        $prompt .= "   - Bobot Skor\n";
        $prompt .= "3. Ringkasan distribusi soal\n";
        $prompt .= "4. Kriteria penilaian\n\n";

        $prompt .= "=== FORMAT OUTPUT ===\n";
        $prompt .= "Berikan output dalam format JSON yang valid:\n\n";
        $prompt .= "```json\n";
        $prompt .= "{\n";
        $prompt .= "  \"exam_info\": {\n";
        $prompt .= "    \"subject\": \"Nama Mata Pelajaran\",\n";
        $prompt .= "    \"class\": \"Kelas\",\n";
        $prompt .= "    \"semester\": \"1/2\",\n";
        $prompt .= "    \"exam_type\": \"Jenis Ujian\",\n";
        $prompt .= "    \"duration\": \"90 menit\",\n";
        $prompt .= "    \"total_score\": 100\n";
        $prompt .= "  },\n";
        $prompt .= "  \"question_mapping\": [\n";
        $prompt .= "    {\n";
        $prompt .= "      \"no\": 1,\n";
        $prompt .= "      \"competency\": \"Kompetensi Dasar\",\n";
        $prompt .= "      \"material\": \"Materi Pokok\",\n";
        $prompt .= "      \"indicator\": \"Indikator soal spesifik\",\n";
        $prompt .= "      \"cognitive_level\": \"C1-C6\",\n";
        $prompt .= "      \"question_type\": \"PG/Essay\",\n";
        $prompt .= "      \"question_number\": \"1\",\n";
        $prompt .= "      \"score_weight\": 5\n";
        $prompt .= "    }\n";
        $prompt .= "  ],\n";
        $prompt .= "  \"summary\": {\n";
        $prompt .= "    \"total_questions\": 10,\n";
        $prompt .= "    \"multiple_choice\": 8,\n";
        $prompt .= "    \"essay\": 2,\n";
        $prompt .= "    \"cognitive_distribution\": {\n";
        $prompt .= "      \"C1\": 2, \"C2\": 2, \"C3\": 2, \"C4\": 2, \"C5\": 1, \"C6\": 1\n";
        $prompt .= "    }\n";
        $prompt .= "  },\n";
        $prompt .= "  \"assessment_criteria\": {\n";
        $prompt .= "    \"scoring_guide\": \"Panduan penskoran\",\n";
        $prompt .= "    \"time_allocation\": \"Alokasi waktu per jenis soal\",\n";
        $prompt .= "    \"special_instructions\": \"Instruksi khusus\"\n";
        $prompt .= "  }\n";
        $prompt .= "}\n";
        $prompt .= "```\n\n";

        $prompt .= "=== PEDOMAN PENTING ===\n";
        $prompt .= "1. Gunakan bahasa Indonesia yang baik dan benar\n";
        $prompt .= "2. Pastikan pemetaan soal sesuai dengan indikator pembelajaran\n";
        $prompt .= "3. Distribusi tingkat kognitif harus seimbang dan sesuai kurikulum\n";
        $prompt .= "4. Bobot skor disesuaikan dengan tingkat kesulitan soal\n";
        $prompt .= "5. Berikan indikator soal yang spesifik dan terukur\n";
        $prompt .= "6. Sesuaikan dengan standar kisi-kisi ujian Indonesia\n";
        $prompt .= "7. Berikan HANYA JSON yang valid, tanpa teks tambahan\n";

        return $prompt;
    }

    private function parseBlueprintResponse($response) {
        try {
            // First, decode the API response
            $data = json_decode($response, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                $this->logDebug("Failed to decode Single RPP Blueprint API response JSON: " . json_last_error_msg());
                $this->logDebug("Raw Single RPP Blueprint response: " . $response);
                throw new Exception("Invalid Single RPP Blueprint API response format: " . json_last_error_msg());
            }

            if (!isset($data['candidates'][0]['content']['parts'][0]['text'])) {
                $this->logDebug("Missing expected structure in Single RPP Blueprint API response");
                $this->logDebug("Single RPP Blueprint Response structure: " . json_encode($data, JSON_PRETTY_PRINT));
                throw new Exception("Format Single RPP Blueprint response API tidak valid - missing content text");
            }

            $content = $data['candidates'][0]['content']['parts'][0]['text'];
            $this->logDebug("Extracted Single RPP Blueprint content from API: " . $content);

            // Clean and extract JSON from content using the same method as Multi-RPP
            $cleaned_content = $this->cleanAndExtractJson($content);
            $this->logDebug("Cleaned Single RPP Blueprint content: " . $cleaned_content);

            $blueprint_data = json_decode($cleaned_content, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                $this->logDebug("Single RPP Blueprint JSON Parse Error: " . json_last_error_msg());
                $this->logDebug("Single RPP Blueprint Cleaned content that failed to parse: " . $cleaned_content);

                // Try alternative parsing methods
                $blueprint_data = $this->tryAlternativeParsing($cleaned_content);

                if (!$blueprint_data) {
                    throw new Exception("Gagal parsing Single RPP Blueprint JSON response: " . json_last_error_msg() . ". Content: " . substr($cleaned_content, 0, 200) . "...");
                }
            }

            $this->logDebug("Successfully parsed Single RPP Blueprint data");

            // Standardize the blueprint format to match Multi-RPP structure
            return $this->standardizeSingleRppBlueprint($blueprint_data);

        } catch (Exception $e) {
            $this->logDebug("Exception in parseBlueprintResponse: " . $e->getMessage());
            throw $e;
        }
    }

    private function standardizeSingleRppBlueprint($raw_blueprint_data) {
        // Convert Single RPP blueprint to standardized format matching Multi-RPP
        $standardized = [
            'exam_info' => $this->formatSingleRppExamInfo($raw_blueprint_data),
            'learning_objectives' => $this->formatSingleRppObjectives($raw_blueprint_data),
            'question_distribution' => $this->formatSingleRppDistribution($raw_blueprint_data),
            'cognitive_mapping' => $this->extractCognitiveMapping($raw_blueprint_data),
            'blueprint_table' => $this->formatSingleRppBlueprintTable($raw_blueprint_data),
            'summary' => $raw_blueprint_data['summary'] ?? [],
            'assessment_criteria' => $raw_blueprint_data['assessment_criteria'] ?? []
        ];

        return $standardized;
    }

    private function formatSingleRppExamInfo($blueprint_data) {
        // Ensure exam_info has proper structure
        $exam_info = $blueprint_data['exam_info'] ?? [];

        // Add default values if missing
        if (empty($exam_info['title'])) {
            $exam_info['title'] = 'Kisi-kisi Ujian';
        }

        return $exam_info;
    }

    private function formatSingleRppObjectives($blueprint_data) {
        // Format learning objectives to match Multi-RPP structure
        if (isset($blueprint_data['learning_objectives'])) {
            return [[
                'chapter' => 'Chapter 1',
                'objectives' => is_array($blueprint_data['learning_objectives'])
                    ? $blueprint_data['learning_objectives']
                    : [$blueprint_data['learning_objectives']]
            ]];
        }

        return [];
    }

    private function formatSingleRppDistribution($blueprint_data) {
        // Format question distribution to match Multi-RPP structure
        $distribution = [
            'by_chapter' => ['Chapter 1' => $blueprint_data['summary']['total_questions'] ?? 0],
            'by_type' => [
                'multiple_choice' => $blueprint_data['summary']['multiple_choice'] ?? 0,
                'essay' => $blueprint_data['summary']['essay'] ?? 0
            ]
        ];

        return $distribution;
    }

    private function extractCognitiveMapping($blueprint_data) {
        // Extract cognitive mapping from various possible locations
        if (isset($blueprint_data['cognitive_mapping'])) {
            return $blueprint_data['cognitive_mapping'];
        }

        // Check in summary.cognitive_distribution
        if (isset($blueprint_data['summary']['cognitive_distribution'])) {
            return $blueprint_data['summary']['cognitive_distribution'];
        }

        // Check in summary.cognitive_mapping
        if (isset($blueprint_data['summary']['cognitive_mapping'])) {
            return $blueprint_data['summary']['cognitive_mapping'];
        }

        // Generate default cognitive mapping if not provided
        return [
            'C1' => 0, 'C2' => 0, 'C3' => 0,
            'C4' => 0, 'C5' => 0, 'C6' => 0
        ];
    }

    private function formatSingleRppBlueprintTable($blueprint_data) {
        // Format blueprint table to match Multi-RPP structure
        if (isset($blueprint_data['question_mapping']) && is_array($blueprint_data['question_mapping'])) {
            $table = [];
            foreach ($blueprint_data['question_mapping'] as $index => $mapping) {
                $table[] = [
                    'chapter' => 'Chapter 1',
                    'learning_objective' => $mapping['competency'] ?? $mapping['learning_objective'] ?? '',
                    'indicator' => $mapping['indicator'] ?? $mapping['material'] ?? '',
                    'cognitive_level' => $mapping['cognitive_level'] ?? 'C1',
                    'question_numbers' => isset($mapping['question_number']) ? [$mapping['question_number']] : [$index + 1],
                    'total_questions' => 1
                ];
            }
            return $table;
        }

        return [];
    }

    private function analyzeQuestions($questions) {
        $stats = [
            'multiple_choice' => 0,
            'essay' => 0,
            'regular' => 0,
            'hots_easy' => 0,
            'hots_medium' => 0,
            'hots_hard' => 0
        ];

        foreach ($questions as $question) {
            $stats[$question['question_type']]++;
            $stats[$question['difficulty_level']]++;
        }

        return $stats;
    }

    private function getDifficultyLabel($difficulty) {
        switch ($difficulty) {
            case 'regular': return 'Regular (C1-C3)';
            case 'hots_easy': return 'HOTS Mudah (C4)';
            case 'hots_medium': return 'HOTS Sedang (C4-C5)';
            case 'hots_hard': return 'HOTS Tinggi (C5-C6)';
            default: return 'Regular (C1-C3)';
        }
    }

    public function testConnection() {
        if (empty($this->api_key)) {
            return ['success' => false, 'message' => 'API key tidak ditemukan'];
        }

        try {
            $test_data = [
                'contents' => [
                    [
                        'parts' => [
                            ['text' => 'Test connection. Respond with "OK"']
                        ]
                    ]
                ]
            ];

            $response = $this->makeRequest($test_data);
            return ['success' => true, 'message' => 'Koneksi berhasil'];
        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    // Multi-RPP parsing methods
    private function parseMultiRppResponse($response, $config, $rpp_data_array) {
        try {
            // First, decode the API response
            $data = json_decode($response, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                $this->logDebug("Failed to decode Multi-RPP API response JSON: " . json_last_error_msg());
                $this->logDebug("Raw Multi-RPP response: " . $response);
                throw new Exception("Invalid Multi-RPP API response format: " . json_last_error_msg());
            }

            if (!isset($data['candidates'][0]['content']['parts'][0]['text'])) {
                $this->logDebug("Missing expected structure in Multi-RPP API response");
                $this->logDebug("Multi-RPP Response structure: " . json_encode($data, JSON_PRETTY_PRINT));
                throw new Exception("Format Multi-RPP response API tidak valid - missing content text");
            }

            $content = $data['candidates'][0]['content']['parts'][0]['text'];
            $this->logDebug("Extracted Multi-RPP content from API: " . $content);

            // Clean and extract JSON from content
            $cleaned_content = $this->cleanAndExtractJson($content);
            $this->logDebug("Cleaned Multi-RPP content: " . $cleaned_content);

            $questions_data = json_decode($cleaned_content, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                $this->logDebug("Multi-RPP JSON Parse Error: " . json_last_error_msg());
                $this->logDebug("Multi-RPP Cleaned content that failed to parse: " . $cleaned_content);

                // Try alternative parsing methods
                $questions_data = $this->tryAlternativeParsing($cleaned_content);

                if (!$questions_data) {
                    throw new Exception("Gagal parsing Multi-RPP JSON response: " . json_last_error_msg() . ". Content: " . substr($cleaned_content, 0, 200) . "...");
                }
            }

            if (!isset($questions_data['questions']) || !is_array($questions_data['questions'])) {
                $this->logDebug("Invalid Multi-RPP questions structure in parsed data");
                $this->logDebug("Multi-RPP Parsed data: " . json_encode($questions_data, JSON_PRETTY_PRINT));
                throw new Exception("Format Multi-RPP questions tidak valid dalam response. Expected 'questions' array not found.");
            }

            $this->logDebug("Successfully parsed " . count($questions_data['questions']) . " Multi-RPP questions");

            $formatted_questions = [];
            $rpp_id_map = [];

            // Create RPP ID mapping
            foreach ($rpp_data_array as $index => $rpp) {
                $rpp_id_map[$index + 1] = $rpp['id'];
            }

            foreach ($questions_data['questions'] as $index => $question) {
                if (!isset($question['question_text']) || empty($question['question_text'])) {
                    continue;
                }

                $source_chapter = $question['source_chapter'] ?? 1;
                $formatted_question = [
                    'id' => $index + 1,
                    'source_rpp_id' => $rpp_id_map[$source_chapter] ?? $rpp_data_array[0]['id'],
                    'source_chapter' => $source_chapter,
                    'question_text' => $question['question_text'],
                    'question_type' => $question['question_type'] ?? 'multiple_choice',
                    'difficulty_level' => $question['difficulty_level'] ?? 'regular',
                    'cognitive_level' => $question['cognitive_level'] ?? 'C1',
                    'category' => 'Generated',
                    'source_type' => 'generated'
                ];

                // Only add explanation for multiple choice questions
                if (($question['question_type'] ?? 'multiple_choice') === 'multiple_choice') {
                    $formatted_question['explanation'] = $question['explanation'] ?? '';
                }

                if ($formatted_question['question_type'] === 'multiple_choice') {
                    // Validate options for multi-RPP questions
                    $options = $question['options'] ?? [];
                    if (is_array($options) && count($options) >= 2) {
                        // Filter out empty options
                        $valid_options = array_filter($options, function($option) {
                            return !empty(trim($option));
                        });

                        if (count($valid_options) >= 2) {
                            $formatted_question['options'] = array_values($valid_options); // Re-index array
                            $formatted_question['correct_answer'] = $question['correct_answer'] ?? 'A';
                        } else {
                            $formatted_question['options'] = null;
                            $formatted_question['correct_answer'] = null;
                        }
                    } else {
                        $formatted_question['options'] = null;
                        $formatted_question['correct_answer'] = null;
                    }
                } else {
                    $formatted_question['options'] = null;
                    $formatted_question['correct_answer'] = null;
                }

                $formatted_questions[] = $formatted_question;
            }

            return $formatted_questions;

        } catch (Exception $e) {
            $this->logDebug("Exception in parseMultiRppResponse: " . $e->getMessage());
            throw $e;
        }
    }

    private function parseMultiRppBlueprintResponse($response, $exam_data, $rpp_data_array, $questions_data) {
        try {
            // First, decode the API response
            $data = json_decode($response, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                $this->logDebug("Failed to decode Multi-RPP Blueprint API response JSON: " . json_last_error_msg());
                $this->logDebug("Raw Multi-RPP Blueprint response: " . $response);
                throw new Exception("Invalid Multi-RPP Blueprint API response format: " . json_last_error_msg());
            }

            if (!isset($data['candidates'][0]['content']['parts'][0]['text'])) {
                $this->logDebug("Missing expected structure in Multi-RPP Blueprint API response");
                $this->logDebug("Multi-RPP Blueprint Response structure: " . json_encode($data, JSON_PRETTY_PRINT));
                throw new Exception("Format Multi-RPP Blueprint response API tidak valid - missing content text");
            }

            $content = $data['candidates'][0]['content']['parts'][0]['text'];
            $this->logDebug("Extracted Multi-RPP Blueprint content from API: " . $content);

            // Clean and extract JSON from content
            $cleaned_content = $this->cleanAndExtractJson($content);
            $this->logDebug("Cleaned Multi-RPP Blueprint content: " . $cleaned_content);

            $blueprint_data = json_decode($cleaned_content, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                $this->logDebug("Multi-RPP Blueprint JSON Parse Error: " . json_last_error_msg());
                $this->logDebug("Multi-RPP Blueprint Cleaned content that failed to parse: " . $cleaned_content);

                // Try alternative parsing methods
                $blueprint_data = $this->tryAlternativeParsing($cleaned_content);

                if (!$blueprint_data) {
                    throw new Exception("Gagal parsing Multi-RPP Blueprint JSON response: " . json_last_error_msg() . ". Content: " . substr($cleaned_content, 0, 200) . "...");
                }
            }

            $this->logDebug("Successfully parsed Multi-RPP Blueprint data");

            // Ensure required structure
            $blueprint = [
                'exam_info' => $blueprint_data['exam_info'] ?? [
                    'title' => $exam_data['exam_title'],
                    'type' => $exam_data['exam_type'],
                    'subject' => $rpp_data_array[0]['nama_mapel'] ?? '',
                    'duration' => $exam_data['exam_duration'] . ' menit',
                    'total_score' => $exam_data['total_score']
                ],
                'learning_objectives' => $blueprint_data['learning_objectives'] ?? [],
                'question_distribution' => $blueprint_data['question_distribution'] ?? [],
                'cognitive_mapping' => $blueprint_data['cognitive_mapping'] ?? [],
                'blueprint_table' => $blueprint_data['blueprint_table'] ?? [],
                'summary' => $this->generateMultiRppSummary($questions_data, $rpp_data_array),
                'chapters' => $this->formatChapterInfo($rpp_data_array)
            ];

            return $blueprint;

        } catch (Exception $e) {
            $this->logDebug("Exception in parseMultiRppBlueprintResponse: " . $e->getMessage());
            throw $e;
        }
    }

    private function analyzeMultiRppQuestions($questions_data) {
        $stats = [
            'total' => count($questions_data),
            'multiple_choice' => 0,
            'essay' => 0,
            'by_chapter' => [],
            'by_difficulty' => [
                'regular' => 0,
                'hots_easy' => 0,
                'hots_medium' => 0,
                'hots_hard' => 0
            ],
            'by_cognitive' => [
                'C1' => 0, 'C2' => 0, 'C3' => 0,
                'C4' => 0, 'C5' => 0, 'C6' => 0
            ]
        ];

        foreach ($questions_data as $question) {
            // Count by type
            if ($question['question_type'] === 'multiple_choice') {
                $stats['multiple_choice']++;
            } else {
                $stats['essay']++;
            }

            // Count by chapter
            $chapter = $question['source_chapter'] ?? 1;
            if (!isset($stats['by_chapter'][$chapter])) {
                $stats['by_chapter'][$chapter] = 0;
            }
            $stats['by_chapter'][$chapter]++;

            // Count by difficulty
            $difficulty = $question['difficulty_level'] ?? 'regular';
            if (isset($stats['by_difficulty'][$difficulty])) {
                $stats['by_difficulty'][$difficulty]++;
            }

            // Count by cognitive level
            $cognitive = $question['cognitive_level'] ?? 'C1';
            if (isset($stats['by_cognitive'][$cognitive])) {
                $stats['by_cognitive'][$cognitive]++;
            }
        }

        return $stats;
    }

    private function generateMultiRppSummary($questions_data, $rpp_data_array) {
        $stats = $this->analyzeMultiRppQuestions($questions_data);

        return [
            'total_questions' => $stats['total'],
            'multiple_choice' => $stats['multiple_choice'],
            'essay' => $stats['essay'],
            'total_chapters' => count($rpp_data_array),
            'difficulty_distribution' => $stats['by_difficulty'],
            'cognitive_distribution' => $stats['by_cognitive'],
            'chapter_distribution' => $stats['by_chapter']
        ];
    }

    private function formatChapterInfo($rpp_data_array) {
        $chapters = [];
        foreach ($rpp_data_array as $index => $rpp) {
            $chapters[] = [
                'number' => $index + 1,
                'title' => $rpp['tema_subtema'],
                'subject' => $rpp['nama_mapel'],
                'material' => $rpp['materi_pokok'],
                'learning_objectives' => $rpp['tujuan_pembelajaran'],
                'basic_competency' => $rpp['kompetensi_dasar']
            ];
        }
        return $chapters;
    }

    // Enhanced JSON parsing and debugging methods
    private function logDebug($message, $data = null) {
        // Use debug helper function if available
        if (function_exists('debugGeminiApi')) {
            debugGeminiApi($message, $data);
        } else {
            // Fallback to standard error logging
            if (defined('DEBUG_GEMINI_API') && DEBUG_GEMINI_API) {
                error_log("[GeminiApi Debug] " . $message);
                if ($data !== null) {
                    error_log("[GeminiApi Debug Data] " . print_r($data, true));
                }
            }
            // Always log critical errors
            if (strpos($message, 'Error') !== false || strpos($message, 'Failed') !== false) {
                error_log("[GeminiApi] " . $message);
            }
        }
    }

    private function cleanAndExtractJson($content) {
        // Remove any leading/trailing whitespace
        $content = trim($content);

        $this->logDebug("Original content analysis", [
            'length' => strlen($content),
            'starts_with' => substr($content, 0, 100),
            'ends_with' => substr($content, -100)
        ]);

        // Analyze problematic content if debug function is available
        if (function_exists('analyzeProblematicJson')) {
            analyzeProblematicJson($content, 'Before cleaning');
        }

        // Remove markdown code blocks (multiple patterns)
        $patterns = [
            '/^```json\s*/m',
            '/^```JSON\s*/m',
            '/^```\s*/m',
            '/\s*```$/m',
            '/```json/i',
            '/```JSON/i',
            '/```/'
        ];

        foreach ($patterns as $pattern) {
            $before_length = strlen($content);
            $content = preg_replace($pattern, '', $content);
            $after_length = strlen($content);

            if ($before_length !== $after_length) {
                $this->logDebug("Pattern '$pattern' removed " . ($before_length - $after_length) . " characters");
            }
        }

        // Remove any explanatory text before JSON
        $content = preg_replace('/^[^{]*(?=\{)/s', '', $content);

        // Remove any explanatory text after JSON
        $content = preg_replace('/\}[^}]*$/s', '}', $content);

        // Try to find JSON content between curly braces (greedy match)
        if (preg_match('/\{.*\}/s', $content, $matches)) {
            $this->logDebug("Found JSON block with regex match");
            $content = $matches[0];
        } else {
            $this->logDebug("No JSON block found with regex - content may be malformed");
        }

        // Clean up common issues
        $content = $this->fixCommonJsonIssues($content);

        $this->logDebug("Final cleaned content", [
            'length' => strlen($content),
            'preview' => substr($content, 0, 200) . (strlen($content) > 200 ? '...' : '')
        ]);

        // Validate the cleaned JSON if debug function is available
        if (function_exists('validateJsonString')) {
            validateJsonString($content, 'After cleaning');
        }

        return $content;
    }

    private function fixCommonJsonIssues($content) {
        // Fix trailing commas
        $content = preg_replace('/,(\s*[}\]])/', '$1', $content);

        // Fix unescaped quotes in strings (be very careful here)
        // This is a simplified approach - in production you might want more sophisticated handling

        // Fix single quotes to double quotes for keys and simple values
        $content = preg_replace("/(?<=[:\[,\s])'([^']*)'(?=[,\]\}])/", '"$1"', $content);

        // Remove any non-printable characters except newlines and tabs
        $content = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $content);

        // Fix missing quotes around keys
        $content = preg_replace('/([{,]\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s*:/', '$1"$2":', $content);

        return $content;
    }

    private function tryAlternativeParsing($content) {
        $this->logDebug("Trying alternative parsing methods");

        // Method 1: Try to extract just the questions array
        if (preg_match('/"questions"\s*:\s*(\[.*?\])/s', $content, $matches)) {
            $questions_json = '{"questions":' . $matches[1] . '}';
            $this->logDebug("Trying questions array extraction: " . substr($questions_json, 0, 200) . "...");

            $data = json_decode($questions_json, true);
            if (json_last_error() === JSON_ERROR_NONE && isset($data['questions'])) {
                $this->logDebug("Successfully parsed with questions array extraction");
                return $data;
            }
        }

        // Method 2: Try to fix bracket mismatches
        $fixed_content = $this->fixBracketMismatches($content);
        if ($fixed_content !== $content) {
            $this->logDebug("Trying bracket mismatch fix: " . substr($fixed_content, 0, 200) . "...");

            $data = json_decode($fixed_content, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                $this->logDebug("Successfully parsed with bracket fix");
                return $data;
            }
        }

        // Method 3: Try to parse line by line for partial content
        $partial_data = $this->parsePartialJson($content);
        if ($partial_data) {
            $this->logDebug("Successfully parsed with partial parsing");
            return $partial_data;
        }

        $this->logDebug("All alternative parsing methods failed");
        return false;
    }

    private function fixBracketMismatches($content) {
        $open_braces = substr_count($content, '{');
        $close_braces = substr_count($content, '}');
        $open_brackets = substr_count($content, '[');
        $close_brackets = substr_count($content, ']');

        // Add missing closing braces
        if ($open_braces > $close_braces) {
            $content .= str_repeat('}', $open_braces - $close_braces);
        }

        // Add missing closing brackets
        if ($open_brackets > $close_brackets) {
            $content .= str_repeat(']', $open_brackets - $close_brackets);
        }

        return $content;
    }

    private function parsePartialJson($content) {
        // Try to extract at least some questions even if JSON is incomplete
        $questions = [];

        // Look for question patterns
        if (preg_match_all('/"question_text"\s*:\s*"([^"]*)"/', $content, $matches)) {
            foreach ($matches[1] as $index => $question_text) {
                $questions[] = [
                    'id' => $index + 1,
                    'question_text' => $question_text,
                    'question_type' => 'multiple_choice', // default
                    'difficulty_level' => 'regular', // default
                    'cognitive_level' => 'C1', // default
                    'options' => ['A. Option 1', 'B. Option 2', 'C. Option 3', 'D. Option 4'],
                    'correct_answer' => 'A',
                    'category' => 'Generated'
                ];
            }
        }

        if (!empty($questions)) {
            return ['questions' => $questions];
        }

        return false;
    }

    // Essay Answer Generation Methods
    public function generateEssayAnswer($question_text, $rpp_context = '', $question_context = []) {
        if (empty($this->api_key)) {
            throw new Exception("Gemini API key tidak ditemukan. Silakan konfigurasi API key terlebih dahulu.");
        }

        $prompt = $this->buildEssayAnswerPrompt($question_text, $rpp_context, $question_context);

        $data = [
            'contents' => [
                [
                    'parts' => [
                        ['text' => $prompt]
                    ]
                ]
            ],
            'generationConfig' => [
                'temperature' => 0.7,
                'topK' => 40,
                'topP' => 0.95,
                'maxOutputTokens' => 2048,
            ]
        ];

        $response = $this->makeRequest($data);

        if (!$response) {
            throw new Exception("Gagal menghubungi API Gemini");
        }

        // Decode JSON response
        $response_data = json_decode($response, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception("Invalid API response JSON: " . json_last_error_msg());
        }

        return $this->parseEssayAnswerResponse($response_data);
    }

    public function generateBulkEssayAnswers($questions_data, $rpp_context = '') {
        if (empty($this->api_key)) {
            throw new Exception("Gemini API key tidak ditemukan. Silakan konfigurasi API key terlebih dahulu.");
        }

        $prompt = $this->buildBulkEssayAnswerPrompt($questions_data, $rpp_context);

        $data = [
            'contents' => [
                [
                    'parts' => [
                        ['text' => $prompt]
                    ]
                ]
            ],
            'generationConfig' => [
                'temperature' => 0.7,
                'topK' => 40,
                'topP' => 0.95,
                'maxOutputTokens' => 6144, // Increased for bulk generation
            ]
        ];

        $response = $this->makeRequest($data);

        if (!$response) {
            throw new Exception("Gagal menghubungi API Gemini");
        }

        // Decode JSON response
        $response_data = json_decode($response, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception("Invalid API response JSON: " . json_last_error_msg());
        }

        return $this->parseBulkEssayAnswerResponse($response_data, $questions_data);
    }

    private function buildEssayAnswerPrompt($question_text, $rpp_context = '', $question_context = []) {
        $prompt = "Anda adalah seorang guru ahli yang akan membuat jawaban yang diharapkan untuk soal essay.\n\n";

        if (!empty($rpp_context)) {
            $prompt .= "KONTEKS PEMBELAJARAN:\n";
            $prompt .= $rpp_context . "\n\n";
        }

        if (!empty($question_context)) {
            $prompt .= "INFORMASI SOAL:\n";
            if (isset($question_context['difficulty_level'])) {
                $prompt .= "- Tingkat Kesulitan: " . $question_context['difficulty_level'] . "\n";
            }
            if (isset($question_context['category'])) {
                $prompt .= "- Kategori: " . $question_context['category'] . "\n";
            }
            $prompt .= "\n";
        }

        $prompt .= "SOAL ESSAY:\n";
        $prompt .= $question_text . "\n\n";

        $prompt .= "TUGAS ANDA:\n";
        $prompt .= "Buatlah jawaban yang diharapkan untuk soal essay di atas dengan format JSON berikut:\n\n";

        $prompt .= "{\n";
        $prompt .= '  "expected_answer": "Jawaban lengkap dan komprehensif yang diharapkan dari siswa",';
        $prompt .= "\n";
        $prompt .= '  "answer_points": "Poin-poin kunci yang harus ada dalam jawaban siswa (pisahkan dengan bullet points)",';
        $prompt .= "\n";
        $prompt .= '  "scoring_rubric": {';
        $prompt .= "\n";
        $prompt .= '    "excellent": "Kriteria untuk nilai sangat baik (90-100)",';
        $prompt .= "\n";
        $prompt .= '    "good": "Kriteria untuk nilai baik (80-89)",';
        $prompt .= "\n";
        $prompt .= '    "fair": "Kriteria untuk nilai cukup (70-79)",';
        $prompt .= "\n";
        $prompt .= '    "poor": "Kriteria untuk nilai kurang (60-69)"';
        $prompt .= "\n";
        $prompt .= '  },';
        $prompt .= "\n";
        $prompt .= '  "generation_metadata": {';
        $prompt .= "\n";
        $prompt .= '    "confidence_score": 0.95,';
        $prompt .= "\n";
        $prompt .= '    "reasoning": "Alasan mengapa jawaban ini sesuai dengan konteks pembelajaran"';
        $prompt .= "\n";
        $prompt .= '  }';
        $prompt .= "\n";
        $prompt .= "}\n\n";

        $prompt .= "PANDUAN:\n";
        $prompt .= "1. Jawaban harus sesuai dengan konteks pembelajaran dan tingkat kelas\n";
        $prompt .= "2. Gunakan bahasa Indonesia yang baik dan benar\n";
        $prompt .= "3. Jawaban harus komprehensif namun tidak terlalu panjang\n";
        $prompt .= "4. Poin-poin kunci harus mencakup aspek utama yang dinilai\n";
        $prompt .= "5. Rubrik penilaian harus jelas dan dapat diukur\n";
        $prompt .= "6. Berikan hanya JSON yang valid, tanpa teks tambahan\n";

        return $prompt;
    }

    private function buildBulkEssayAnswerPrompt($questions_data, $rpp_context = '') {
        $prompt = "Anda adalah seorang guru ahli yang akan membuat jawaban yang diharapkan untuk beberapa soal essay sekaligus.\n\n";

        if (!empty($rpp_context)) {
            $prompt .= "KONTEKS PEMBELAJARAN:\n";
            $prompt .= $rpp_context . "\n\n";
        }

        $prompt .= "DAFTAR SOAL ESSAY:\n";
        foreach ($questions_data as $index => $question) {
            $prompt .= "SOAL " . ($index + 1) . " (ID: " . $question['id'] . "):\n";
            $prompt .= $question['question_text'] . "\n";
            if (isset($question['difficulty_level'])) {
                $prompt .= "Tingkat: " . $question['difficulty_level'] . "\n";
            }
            if (isset($question['category'])) {
                $prompt .= "Kategori: " . $question['category'] . "\n";
            }
            $prompt .= "\n";
        }

        $prompt .= "TUGAS ANDA:\n";
        $prompt .= "Buatlah jawaban yang diharapkan untuk semua soal essay di atas dengan format JSON berikut:\n\n";

        $prompt .= "{\n";
        $prompt .= '  "answers": [';
        $prompt .= "\n";
        $prompt .= '    {';
        $prompt .= "\n";
        $prompt .= '      "question_id": "ID soal",';
        $prompt .= "\n";
        $prompt .= '      "expected_answer": "Jawaban lengkap dan komprehensif",';
        $prompt .= "\n";
        $prompt .= '      "answer_points": "Poin-poin kunci (bullet points)",';
        $prompt .= "\n";
        $prompt .= '      "scoring_rubric": {';
        $prompt .= "\n";
        $prompt .= '        "excellent": "Kriteria sangat baik (90-100)",';
        $prompt .= "\n";
        $prompt .= '        "good": "Kriteria baik (80-89)",';
        $prompt .= "\n";
        $prompt .= '        "fair": "Kriteria cukup (70-79)",';
        $prompt .= "\n";
        $prompt .= '        "poor": "Kriteria kurang (60-69)"';
        $prompt .= "\n";
        $prompt .= '      },';
        $prompt .= "\n";
        $prompt .= '      "generation_metadata": {';
        $prompt .= "\n";
        $prompt .= '        "confidence_score": 0.95,';
        $prompt .= "\n";
        $prompt .= '        "reasoning": "Alasan jawaban"';
        $prompt .= "\n";
        $prompt .= '      }';
        $prompt .= "\n";
        $prompt .= '    }';
        $prompt .= "\n";
        $prompt .= '  ]';
        $prompt .= "\n";
        $prompt .= "}\n\n";

        $prompt .= "PANDUAN:\n";
        $prompt .= "1. Buat jawaban untuk SEMUA soal yang diberikan\n";
        $prompt .= "2. Setiap jawaban harus sesuai dengan konteks dan tingkat kesulitan\n";
        $prompt .= "3. Gunakan bahasa Indonesia yang baik dan benar\n";
        $prompt .= "4. Jawaban harus komprehensif namun tidak terlalu panjang\n";
        $prompt .= "5. Rubrik penilaian harus jelas dan dapat diukur\n";
        $prompt .= "6. Berikan hanya JSON yang valid, tanpa teks tambahan\n";

        return $prompt;
    }

    private function parseEssayAnswerResponse($response) {
        try {

            // Check if response has error
            if (isset($response['error'])) {
                throw new Exception("Gemini API Error: " . $response['error']['message'] ?? 'Unknown error');
            }

            // Check if response has candidates
            if (!isset($response['candidates']) || empty($response['candidates'])) {
                throw new Exception("No candidates in response. Response keys: " . implode(', ', array_keys($response)));
            }

            // Check if first candidate has content
            if (!isset($response['candidates'][0]['content'])) {
                $candidate_keys = array_keys($response['candidates'][0] ?? []);
                throw new Exception("No content in first candidate. Candidate keys: " . implode(', ', $candidate_keys));
            }

            // Check if content has parts
            if (!isset($response['candidates'][0]['content']['parts']) || empty($response['candidates'][0]['content']['parts'])) {
                $content_keys = array_keys($response['candidates'][0]['content'] ?? []);
                throw new Exception("No parts in content. Content keys: " . implode(', ', $content_keys));
            }

            // Check if first part has text
            if (!isset($response['candidates'][0]['content']['parts'][0]['text'])) {
                $part_keys = array_keys($response['candidates'][0]['content']['parts'][0] ?? []);
                throw new Exception("No text in first part. Part keys: " . implode(', ', $part_keys));
            }

            $content = $response['candidates'][0]['content']['parts'][0]['text'];

            // Clean the content
            $content = trim($content);
            $content = preg_replace('/^```json\s*/', '', $content);
            $content = preg_replace('/\s*```$/', '', $content);

            $data = json_decode($content, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new Exception("Invalid JSON response: " . json_last_error_msg() . ". Content: " . substr($content, 0, 500));
            }

            // Validate required fields
            if (!isset($data['expected_answer']) || empty($data['expected_answer'])) {
                throw new Exception("Expected answer tidak ditemukan dalam response");
            }

            // Set defaults for optional fields
            if (!isset($data['answer_points'])) {
                $data['answer_points'] = '';
            }

            if (!isset($data['scoring_rubric'])) {
                $data['scoring_rubric'] = [
                    'excellent' => 'Jawaban lengkap dan tepat (90-100)',
                    'good' => 'Jawaban baik dengan sedikit kekurangan (80-89)',
                    'fair' => 'Jawaban cukup namun kurang detail (70-79)',
                    'poor' => 'Jawaban kurang tepat atau tidak lengkap (60-69)'
                ];
            }

            if (!isset($data['generation_metadata'])) {
                $data['generation_metadata'] = [
                    'confidence_score' => 0.8,
                    'reasoning' => 'Generated by AI'
                ];
            }

            return $data;

        } catch (Exception $e) {
            throw new Exception("Gagal memparse response untuk jawaban essay: " . $e->getMessage());
        }
    }

    private function parseBulkEssayAnswerResponse($response, $questions_data) {
        try {
            if (!isset($response['candidates'][0]['content']['parts'][0]['text'])) {
                throw new Exception("Format response tidak valid");
            }

            $content = $response['candidates'][0]['content']['parts'][0]['text'];

            // Clean the content
            $content = trim($content);
            $content = preg_replace('/^```json\s*/', '', $content);
            $content = preg_replace('/\s*```$/', '', $content);

            $data = json_decode($content, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new Exception("Invalid JSON response: " . json_last_error_msg());
            }

            if (!isset($data['answers']) || !is_array($data['answers'])) {
                throw new Exception("Answers array tidak ditemukan dalam response");
            }

            $processed_answers = [];

            foreach ($data['answers'] as $answer) {
                if (!isset($answer['question_id']) || !isset($answer['expected_answer'])) {
                    continue; // Skip invalid answers
                }

                // Set defaults for optional fields
                if (!isset($answer['answer_points'])) {
                    $answer['answer_points'] = '';
                }

                if (!isset($answer['scoring_rubric'])) {
                    $answer['scoring_rubric'] = [
                        'excellent' => 'Jawaban lengkap dan tepat (90-100)',
                        'good' => 'Jawaban baik dengan sedikit kekurangan (80-89)',
                        'fair' => 'Jawaban cukup namun kurang detail (70-79)',
                        'poor' => 'Jawaban kurang tepat atau tidak lengkap (60-69)'
                    ];
                }

                if (!isset($answer['generation_metadata'])) {
                    $answer['generation_metadata'] = [
                        'confidence_score' => 0.8,
                        'reasoning' => 'Generated by AI'
                    ];
                }

                $processed_answers[] = $answer;
            }

            return ['answers' => $processed_answers];

        } catch (Exception $e) {
            throw new Exception("Gagal memparse response untuk bulk jawaban essay: " . $e->getMessage());
        }
    }
}
?>
