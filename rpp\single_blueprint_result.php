<?php
require_once __DIR__ . '/../middleware/auth.php';
checkGuruAccess();
require_once '../template/header.php';
require_once '../config/database.php';
require_once '../models/ExamBlueprint.php';
require_once '../models/Rpp.php';
require_once '../models/Guru.php';

// Validasi parameter
if (!isset($_GET['blueprint_id'])) {
    $_SESSION['error'] = "ID kisi-kisi tidak ditemukan.";
    header("Location: blueprint_list.php");
    exit();
}

$blueprint_id = $_GET['blueprint_id'];

// Dapatkan guru_id
$guru = new Guru();
$stmt = $guru->getByUserId($_SESSION['user_id']);
if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $guru_id = $row['id'];
    $guru_data = $row;
} else {
    die("Data guru tidak ditemukan");
}

// Ambil data kisi-kisi
$examBlueprint = new ExamBlueprint();
$stmt = $examBlueprint->getOne($blueprint_id);
$blueprint_data = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$blueprint_data || $blueprint_data['guru_id'] != $guru_id) {
    $_SESSION['error'] = "Kisi-kisi tidak ditemukan atau bukan milik Anda.";
    header("Location: blueprint_list.php");
    exit();
}

// Decode JSON data
$generated_blueprint = json_decode($blueprint_data['blueprint_data'], true);
$exam_info = json_decode($blueprint_data['exam_info'], true);
$learning_objectives = json_decode($blueprint_data['learning_objectives'], true);
$question_distribution = json_decode($blueprint_data['question_distribution'], true);
$cognitive_mapping = json_decode($blueprint_data['cognitive_mapping'], true);

// Get related RPP data if this is a single RPP blueprint
$rpp_data = null;
if ($blueprint_data['rpp_id']) {
    $rpp = new Rpp();
    $rpp_data = $rpp->getOne($blueprint_data['rpp_id']);
}

$success_msg = isset($_SESSION['success']) ? $_SESSION['success'] : '';
$error_msg = isset($_SESSION['error']) ? $_SESSION['error'] : '';
unset($_SESSION['success'], $_SESSION['error']);
?>

<div class="container-fluid">
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="fas fa-file-alt"></i> Kisi-kisi Ujian RPP
            </h5>
            <div>
                <a href="single_blueprint_export.php?blueprint_id=<?= $blueprint_id ?>&format=pdf" 
                   class="btn btn-danger me-2" target="_blank">
                    <i class="fas fa-file-pdf"></i> Export PDF
                </a>
                <a href="single_blueprint_export.php?blueprint_id=<?= $blueprint_id ?>&format=word" 
                   class="btn btn-primary me-2">
                    <i class="fas fa-file-word"></i> Export Word
                </a>
                <?php if ($rpp_data): ?>
                    <a href="generate_questions.php?rpp_id=<?= $rpp_data['id'] ?>" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-eye"></i> Lihat RPP
                    </a>
                <?php endif; ?>
                <a href="blueprint_list.php" class="btn btn-secondary">
                    <i class="fas fa-list"></i> Daftar Kisi-kisi
                </a>
            </div>
        </div>
        <div class="card-body">
            <?php if ($success_msg): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?= $success_msg ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if ($error_msg): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= $error_msg ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <!-- Blueprint Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-info-circle"></i> Informasi Kisi-kisi</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless table-sm">
                                <tr>
                                    <td width="150"><strong>Judul:</strong></td>
                                    <td><?= htmlspecialchars($blueprint_data['blueprint_title']) ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Jenis:</strong></td>
                                    <td><span class="badge bg-info">Single RPP</span></td>
                                </tr>
                                <?php if ($rpp_data): ?>
                                <tr>
                                    <td><strong>RPP:</strong></td>
                                    <td><?= htmlspecialchars($rpp_data['tema_subtema']) ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Mata Pelajaran:</strong></td>
                                    <td><?= htmlspecialchars($rpp_data['nama_mapel']) ?></td>
                                </tr>
                                <?php endif; ?>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless table-sm">
                                <tr>
                                    <td width="150"><strong>Dibuat:</strong></td>
                                    <td><?= date('d/m/Y H:i', strtotime($blueprint_data['created_at'])) ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Diperbarui:</strong></td>
                                    <td><?= date('d/m/Y H:i', strtotime($blueprint_data['updated_at'])) ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Status:</strong></td>
                                    <td><span class="badge bg-success">Tersimpan</span></td>
                                </tr>
                                <?php if ($rpp_data): ?>
                                <tr>
                                    <td><strong>Kelas:</strong></td>
                                    <td><?= htmlspecialchars($rpp_data['nama_kelas']) ?></td>
                                </tr>
                                <?php endif; ?>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Blueprint Content -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-table"></i> Isi Kisi-kisi</h6>
                </div>
                <div class="card-body">
                    <?php if ($generated_blueprint): ?>
                        <?php include 'blueprint_preview_template.php'; ?>
                    <?php else: ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            Data kisi-kisi tidak dapat ditampilkan. Mungkin format data tidak valid.
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Actions -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-cog"></i> Aksi</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Export & Cetak</h6>
                            <div class="d-flex gap-2 mb-3">
                                <a href="single_blueprint_export.php?blueprint_id=<?= $blueprint_id ?>&format=pdf" 
                                   class="btn btn-danger" target="_blank">
                                    <i class="fas fa-file-pdf"></i> Export PDF
                                </a>
                                <a href="single_blueprint_export.php?blueprint_id=<?= $blueprint_id ?>&format=word" 
                                   class="btn btn-primary">
                                    <i class="fas fa-file-word"></i> Export Word
                                </a>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>Kelola Kisi-kisi</h6>
                            <div class="d-flex gap-2 mb-3">
                                <?php if ($rpp_data): ?>
                                    <a href="single_blueprint_generate.php?rpp_id=<?= $rpp_data['id'] ?>" 
                                       class="btn btn-warning">
                                        <i class="fas fa-edit"></i> Generate Ulang
                                    </a>
                                <?php endif; ?>
                                <button type="button" class="btn btn-danger" onclick="confirmDelete()">
                                    <i class="fas fa-trash"></i> Hapus
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">
                    <i class="fas fa-exclamation-triangle text-danger"></i> Konfirmasi Hapus
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Apakah Anda yakin ingin menghapus kisi-kisi ini?</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Peringatan:</strong> Tindakan ini tidak dapat dibatalkan. Semua data kisi-kisi akan hilang permanen.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                <form action="single_blueprint_delete.php" method="POST" style="display: inline;">
                    <input type="hidden" name="blueprint_id" value="<?= $blueprint_id ?>">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash"></i> Ya, Hapus
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete() {
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}
</script>

<style>
.table th {
    font-weight: 600;
    font-size: 0.9rem;
}

.table td {
    vertical-align: middle;
    font-size: 0.9rem;
}

/* Fix for table-dark header visibility */
.table-dark th {
    background-color: #212529 !important;
    color: #ffffff !important;
    border-color: #32383e !important;
}

.table-dark th,
.table-dark td {
    border-color: #32383e !important;
}

/* Ensure blueprint preview table headers are visible */
.blueprint-preview .table-dark th {
    background-color: #343a40 !important;
    color: #ffffff !important;
    font-weight: 600;
    text-align: center;
}

.blueprint-preview .table th {
    font-weight: 600;
    font-size: 0.9rem;
    color: inherit;
}

.badge {
    font-size: 0.75rem;
}

.card-title {
    color: #495057;
}

.btn-group .btn {
    margin-right: 0.25rem;
}

.alert {
    border-radius: 0.5rem;
}
</style>

<?php require_once '../template/footer.php'; ?>
