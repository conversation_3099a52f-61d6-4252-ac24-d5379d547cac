<?php
require_once __DIR__ . '/../middleware/auth.php';
if (!isset($_SESSION['user_id']) || !in_array($_SESSION['role'], ['guru', 'admin'])) {
    header("Location: ../403.php");
    exit();
}

require_once '../template/header.php';
require_once '../models/Berita.php';
require_once '../config/database.php';

$database = new Database();
$berita = new Berita();
$error = "";
$success = "";

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $berita->judul = $_POST['judul'];
    $berita->isi = $_POST['isi'];
    $berita->created_by = $_SESSION['user_id'];

    // Handle thumbnail upload
    if (isset($_FILES['thumbnail']) && $_FILES['thumbnail']['error'] == 0) {
        $allowed = ['jpg', 'jpeg', 'png', 'gif'];
        $filename = $_FILES['thumbnail']['name'];
        $ext = strtolower(pathinfo($filename, PATHINFO_EXTENSION));

        if (in_array($ext, $allowed)) {
            $new_filename = uniqid() . '.' . $ext;
            $upload_path = '../uploads/berita/' . $new_filename;

            if (move_uploaded_file($_FILES['thumbnail']['tmp_name'], $upload_path)) {
                $berita->thumbnail = $new_filename;
            } else {
                $error = "Gagal mengupload thumbnail";
            }
        } else {
            $error = "Format file tidak diizinkan. Gunakan: " . implode(', ', $allowed);
        }
    }

    if (empty($error)) {
        if ($berita->create()) {
            $success = "Berita berhasil ditambahkan";
            header("Location: index.php?success=1");
            exit();
        } else {
            $error = "Gagal menambahkan berita";
        }
    }
}

$page_title = "Tambah Berita";
?>

<div class="content-wrapper">
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">Tambah Berita</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="../dashboard">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="index.php">Berita</a></li>
                        <li class="breadcrumb-item active">Tambah</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <section class="content">
        <div class="container-fluid">
            <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php echo $error; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>
            <?php if ($success): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo $success; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Form Tambah Berita</h5>
                    <a href="index.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Kembali
                    </a>
                </div>
                <div class="card-body">
                    <form action="" method="POST" enctype="multipart/form-data">
                        <div class="form-group mb-3">
                            <label for="judul">Judul Berita</label>
                            <input type="text" class="form-control" id="judul" name="judul" required>
                        </div>
                        <div class="form-group mb-3">
                            <label for="isi">Isi Berita</label>

                            <!-- Editor Mode Tabs -->
                            <div class="editor-tabs mb-2">
                                <ul class="nav nav-tabs" id="editorTabs" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link active" id="visual-tab" data-bs-toggle="tab" data-bs-target="#visual-editor" type="button" role="tab" aria-controls="visual-editor" aria-selected="true">
                                            <i class="fas fa-eye"></i> Visual
                                        </button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="code-tab" data-bs-toggle="tab" data-bs-target="#code-editor" type="button" role="tab" aria-controls="code-editor" aria-selected="false">
                                            <i class="fas fa-code"></i> Code
                                        </button>
                                    </li>
                                </ul>
                            </div>

                            <!-- Editor Content -->
                            <div class="tab-content" id="editorTabContent">
                                <!-- Visual Editor -->
                                <div class="tab-pane fade show active" id="visual-editor" role="tabpanel" aria-labelledby="visual-tab">
                                    <textarea class="form-control" id="isi" name="isi" rows="10" required></textarea>
                                </div>

                                <!-- Code Editor -->
                                <div class="tab-pane fade" id="code-editor" role="tabpanel" aria-labelledby="code-tab">
                                    <textarea class="form-control" id="isi-code" rows="15" style="font-family: 'Courier New', monospace; font-size: 14px;"></textarea>
                                    <small class="form-text text-muted">
                                        <i class="fas fa-info-circle"></i> Mode HTML - Edit kode HTML secara langsung. Pastikan menggunakan tag HTML yang valid.
                                    </small>
                                </div>
                            </div>
                        </div>
                        <div class="form-group mb-3">
                            <label for="thumbnail">Thumbnail</label>
                            <input type="file" class="form-control" id="thumbnail" name="thumbnail">
                            <small class="form-text text-muted">Format yang diizinkan: JPG, JPEG, PNG, GIF</small>
                        </div>
                        <hr>
                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Simpan
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>
</div>

<script src="https://cdn.tiny.cloud/1/<?php echo $database->getTinyMCEApiKey(); ?>/tinymce/6/tinymce.min.js" referrerpolicy="origin"></script>
<script>
$(document).ready(function() {
    let editorMode = 'visual';
    let tinymceEditor = null;

    // Initialize TinyMCE with complete configuration
    function initTinyMCE() {
        tinymce.init({
            selector: '#isi',
            plugins: 'advlist autolink lists link image charmap print preview hr anchor pagebreak searchreplace wordcount visualblocks visualchars code fullscreen insertdatetime media nonbreaking save table contextmenu directionality emoticons template paste textcolor',
            toolbar: 'undo redo | styleselect | bold italic | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | link image media | forecolor backcolor emoticons',
            toolbar_mode: 'floating',
            height: 300,
            forced_root_block: 'p',
            force_br_newlines: false,
            force_p_newlines: true,
            convert_newlines_to_brs: false,
            remove_linebreaks: false,
            valid_elements: 'p,br,strong,em,ul,ol,li,h1,h2,h3,h4,h5,h6,blockquote,a[href],img[src|alt]',
            valid_children: '+ul[li],+ol[li]',
            setup: function(editor) {
                tinymceEditor = editor;
                editor.on('change', function() {
                    editor.save();
                });
            }
        });
    }

    // Initialize TinyMCE
    initTinyMCE();

    // Handle tab switching
    $('#visual-tab').on('click', function() {
        if (editorMode === 'code') {
            // Switch from code to visual
            const codeContent = $('#isi-code').val();

            // Destroy existing TinyMCE instance if exists
            if (tinymceEditor) {
                tinymce.remove('#isi');
            }

            // Set content to textarea
            $('#isi').val(codeContent);

            // Reinitialize TinyMCE
            setTimeout(function() {
                initTinyMCE();
            }, 100);

            editorMode = 'visual';
        }
    });

    $('#code-tab').on('click', function() {
        if (editorMode === 'visual') {
            // Switch from visual to code
            let visualContent = '';

            if (tinymceEditor) {
                visualContent = tinymceEditor.getContent();
                tinymce.remove('#isi');
                tinymceEditor = null;
            } else {
                visualContent = $('#isi').val();
            }

            // Set content to code editor
            $('#isi-code').val(visualContent);

            editorMode = 'code';
        }
    });

    // Sync content before form submission
    $('form').on('submit', function(e) {
        if (editorMode === 'code') {
            // If in code mode, sync code content to main textarea
            $('#isi').val($('#isi-code').val());
        } else if (tinymceEditor) {
            // If in visual mode, ensure TinyMCE content is saved
            tinymceEditor.save();
        }
    });

    // Form validation (updated to handle both modes)
    $('form').on('submit', function(e) {
        var judul = $('#judul').val().trim();
        var isi = '';

        // Get content based on current mode
        if (editorMode === 'code') {
            isi = $('#isi-code').val().trim();
        } else if (tinymceEditor) {
            isi = tinymceEditor.getContent().trim();
        } else {
            isi = $('#isi').val().trim();
        }

        if (!judul) {
            e.preventDefault();
            alert('Judul berita tidak boleh kosong');
            return false;
        }

        if (!isi) {
            e.preventDefault();
            alert('Isi berita tidak boleh kosong');
            return false;
        }
    });
});
</script>

<?php
require_once '../template/footer.php';
?>
