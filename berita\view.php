<?php
require_once __DIR__ . '/../middleware/auth.php';
if (!isset($_SESSION['user_id']) || !in_array($_SESSION['role'], ['guru', 'admin'])) {
    header("Location: ../403.php");
    exit();
}

require_once '../models/Berita.php';
require_once '../models/KomentarBerita.php';
require_once '../models/LikeBerita.php';
require_once '../models/LikeKomentar.php';
require_once '../config/database.php';

$berita = new Berita();
$komentar = new KomentarBerita();
$likeBerita = new LikeBerita();
$likeKomentar = new LikeKomentar();

if (!isset($_GET['id'])) {
    header("Location: index.php");
    exit();
}

$berita->id = $_GET['id'];
if (!$berita->getOne()) {
    header("Location: index.php");
    exit();
}

$comments = $komentar->getByBerita($berita->id);

$page_title = $berita->judul;
require_once '../template/header.php';
?>

<div class="content-wrapper">
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">Detail Berita</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="../dashboard">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="index.php">Berita</a></li>
                        <li class="breadcrumb-item active">Detail</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <section class="content">
        <div class="container-fluid">
            <!-- Berita Card -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title"><?php echo htmlspecialchars($berita->judul); ?></h3>
                    <div class="card-tools">
                        <small class="text-muted">
                            Dibuat oleh: <?php echo htmlspecialchars($berita->nama_pembuat); ?> |
                            <?php echo date('d/m/Y H:i', strtotime($berita->created_at)); ?>
                        </small>
                    </div>
                </div>
                <div class="card-body">
                    <?php if ($berita->thumbnail): ?>
                        <div class="text-center mb-4">
                            <img src="../uploads/berita/<?php echo htmlspecialchars($berita->thumbnail); ?>"
                                 alt="Thumbnail"
                                 class="img-fluid rounded"
                                 style="max-height: 400px;">
                        </div>
                    <?php endif; ?>

                    <div class="berita-content">
                        <?php echo nl2br($berita->isi); ?>
                    </div>

                    <!-- Like/Dislike Berita -->
                    <?php
                    $likeCountBerita = $likeBerita->getLikeCount($berita->id);
                    $userLikeStatusBerita = $likeBerita->getUserLikeStatus($berita->id, $_SESSION['user_id']);
                    ?>
                    <div class="like-section mt-3" id="berita-<?php echo $berita->id; ?>">
                        <button type="button" onclick="handleLike('berita', <?php echo $berita->id; ?>, 0)"
                                class="btn <?php echo $userLikeStatusBerita === 'like' ? 'btn-primary' : 'btn-outline-primary'; ?> btn-sm">
                            <i class="fas fa-thumbs-up"></i>
                            <span class="like-count"><?php echo $likeCountBerita['like_count'] ?? 0; ?></span>
                        </button>
                        <button type="button" onclick="handleLike('berita', <?php echo $berita->id; ?>, 1)"
                                class="btn <?php echo $userLikeStatusBerita === 'dislike' ? 'btn-danger' : 'btn-outline-danger'; ?> btn-sm">
                            <i class="fas fa-thumbs-down"></i>
                            <span class="dislike-count"><?php echo $likeCountBerita['dislike_count'] ?? 0; ?></span>
                        </button>
                    </div>

                    <hr class="mt-4 mb-4">

                    <!-- Komentar Section -->
                    <div class="comments-section">
                        <h4 class="mb-4">
                            <i class="fas fa-comments"></i> Komentar
                        </h4>

                        <!-- Form Komentar -->
                        <div class="card mb-4">
                            <div class="card-body">
                                <form id="commentForm">
                                    <input type="hidden" id="beritaId" value="<?php echo $berita->id; ?>">
                                    <input type="hidden" id="parentId" value="">
                                    <div class="form-group">
                                        <label for="commentText">Tulis Komentar</label>
                                        <textarea class="form-control" id="commentText" rows="3" placeholder="Tulis komentar Anda di sini..."></textarea>
                                    </div>
                                    <div class="text-right">
                                        <button type="button" id="cancelReply" class="btn btn-secondary" style="display: none;">
                                            Batal Membalas
                                        </button>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-paper-plane"></i> Kirim
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <!-- Daftar Komentar -->
                        <div id="commentsList">
                            <?php
                            while ($row = $comments->fetch(PDO::FETCH_ASSOC)):
                                $margin_left = $row['level'] * 40;
                                $likeCountKomentar = $likeKomentar->getLikeCount($row['id']);
                                $userLikeStatusKomentar = $likeKomentar->getUserLikeStatus($row['id'], $_SESSION['user_id']);
                            ?>
                                <div class="comment-thread mb-3" style="margin-left: <?php echo $margin_left; ?>px;">
                                    <div class="card">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-center mb-2">
                                                <h6 class="mb-0">
                                                    <i class="fas fa-user-circle"></i>
                                                    <?php echo htmlspecialchars($row['nama_user']); ?>
                                                </h6>
                                                <small class="text-muted">
                                                    <?php echo date('d/m/Y H:i', strtotime($row['created_at'])); ?>
                                                </small>
                                            </div>
                                            <p class="card-text"><?php echo nl2br(htmlspecialchars($row['komentar'])); ?></p>
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div class="like-section" id="komentar-<?php echo $row['id']; ?>">
                                                    <button type="button" onclick="handleLike('komentar', <?php echo $row['id']; ?>, 0)"
                                                            class="btn <?php echo $userLikeStatusKomentar === 'like' ? 'btn-primary' : 'btn-outline-primary'; ?> btn-sm">
                                                        <i class="fas fa-thumbs-up"></i>
                                                        <span class="like-count"><?php echo $likeCountKomentar['like_count'] ?? 0; ?></span>
                                                    </button>
                                                    <button type="button" onclick="handleLike('komentar', <?php echo $row['id']; ?>, 1)"
                                                            class="btn <?php echo $userLikeStatusKomentar === 'dislike' ? 'btn-danger' : 'btn-outline-danger'; ?> btn-sm">
                                                        <i class="fas fa-thumbs-down"></i>
                                                        <span class="dislike-count"><?php echo $likeCountKomentar['dislike_count'] ?? 0; ?></span>
                                                    </button>
                                                </div>
                                                <div>
                                                    <button type="button" class="btn btn-sm btn-outline-primary"
                                                            onclick="replyTo(<?php echo $row['id']; ?>, '<?php echo htmlspecialchars($row['nama_user']); ?>')">
                                                        <i class="fas fa-reply"></i> Balas
                                                    </button>
                                                    <?php if ($_SESSION['user_id'] == $row['user_id'] || $_SESSION['role'] == 'admin'): ?>
                                                        <a href="javascript:void(0)" onclick="hapusKomentar(<?php echo $row['id']; ?>)"
                                                           class="btn btn-sm btn-outline-danger">
                                                            <i class="fas fa-trash"></i> Hapus
                                                        </a>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endwhile; ?>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="index.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Kembali
                    </a>
                    <?php if ($_SESSION['user_id'] == $berita->created_by || $_SESSION['role'] == 'admin'): ?>
                        <a href="edit.php?id=<?php echo $berita->id; ?>" class="btn btn-warning">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                        <a href="javascript:void(0)" onclick="hapusBerita(<?php echo $berita->id; ?>)" class="btn btn-danger">
                            <i class="fas fa-trash"></i> Hapus
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </section>
</div>

<script>
$(document).ready(function() {
    // Handle comment submission
    $('#commentForm').on('submit', function(e) {
        e.preventDefault();

        const commentText = $('#commentText').val().trim();
        if (!commentText) {
            Swal.fire('Error', 'Komentar tidak boleh kosong', 'error');
            return;
        }

        $.ajax({
            url: 'add_comment.php',
            method: 'POST',
            data: {
                berita_id: $('#beritaId').val(),
                komentar: commentText,
                parent_id: $('#parentId').val() || null
            },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    Swal.fire('Error', response.message || 'Terjadi kesalahan saat mengirim komentar', 'error');
                }
            },
            error: function() {
                Swal.fire('Error', 'Terjadi kesalahan saat mengirim komentar', 'error');
            }
        });
    });

    // Cancel reply button
    $('#cancelReply').on('click', function() {
        $('#parentId').val('');
        $('#commentText').val('');
        $(this).hide();
        $('#commentForm label').text('Tulis Komentar');
    });
});

function replyTo(commentId, userName) {
    $('#parentId').val(commentId);
    $('#commentText').focus();
    $('#commentForm label').text('Balas komentar ' + userName);
    $('#cancelReply').show();
    $('html, body').animate({
        scrollTop: $('#commentForm').offset().top - 100
    }, 500);
}

function hapusKomentar(id) {
    if (confirm('Apakah Anda yakin ingin menghapus komentar ini?')) {
        $.post('delete_comment.php', {id: id}, function(response) {
            if (response.success) {
                alert('Komentar berhasil dihapus');
                location.reload();
            } else {
                alert(response.message || 'Gagal menghapus komentar');
            }
        }, 'json').fail(function() {
            alert('Terjadi kesalahan saat menghapus komentar');
        });
    }
}

function hapusBerita(id) {
    if (confirm('Apakah Anda yakin ingin menghapus berita ini?')) {
        window.location.href = 'delete.php?id=' + id;
    }
}

function handleLike(type, id, isDislike) {
    $.ajax({
        url: 'handle_like.php',
        type: 'POST',
        dataType: 'json',
        data: {
            type: type,
            id: id,
            is_dislike: isDislike,
            user_id: <?php echo $_SESSION['user_id']; ?>
        },
        success: function(response) {
            if (response.status === 'success') {
                // Update tampilan like/dislike count
                const container = $(`#${type}-${id}`);
                container.find('.like-count').text(response.data.like_count || 0);
                container.find('.dislike-count').text(response.data.dislike_count || 0);

                // Update tampilan tombol
                const likeBtn = container.find('button').eq(0);
                const dislikeBtn = container.find('button').eq(1);

                if (response.data.user_status === 'like') {
                    likeBtn.removeClass('btn-outline-primary').addClass('btn-primary');
                    dislikeBtn.removeClass('btn-danger').addClass('btn-outline-danger');
                } else if (response.data.user_status === 'dislike') {
                    likeBtn.removeClass('btn-primary').addClass('btn-outline-primary');
                    dislikeBtn.removeClass('btn-outline-danger').addClass('btn-danger');
                } else {
                    likeBtn.removeClass('btn-primary').addClass('btn-outline-primary');
                    dislikeBtn.removeClass('btn-danger').addClass('btn-outline-danger');
                }
            } else {
                alert(response.message || 'Terjadi kesalahan saat memproses like/dislike');
            }
        },
        error: function() {
            alert('Terjadi kesalahan saat memproses like/dislike');
        }
    });
}
</script>

<style>
.like-section {
    display: flex;
    gap: 8px;
}

.like-section button {
    display: flex;
    align-items: center;
    gap: 5px;
}

.like-section button i {
    font-size: 14px;
}

.like-count, .dislike-count {
    font-weight: 500;
}
</style>

<?php require_once '../template/footer.php'; ?>
